<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <!--begin::Logo-->
    @if (env('HIDE_LOGO') == false)
        <div class="app-sidebar-logo px-6 col-sm-12 justify-content-center"" id="kt_app_sidebar_logo">
            <!--begin::Logo image-->
            {{-- <a href="/">
                <img alt="Logo" src="{{ url('/') }}/assets/media/logos/bos_logo.svg" class="h-100px mt-10 ms-20  app-sidebar-logo-default theme-light-show text-center" />
                <img alt="Logo" src="{{ url('/') }}/assets/media/logos/bos_logo.svg" class="h-25px app-sidebar-logo-default theme-dark-show" />
                <img alt="Logo" src="{{ url('/') }}/assets/media/logos/bos_logo.svg" class="h-20px app-sidebar-logo-minimize" />
            </a> --}}
            <a href="/">
                <img alt="Logo" src="{{ url('/') }}/assets/img/logo_dodo_pass_46.png" />
            </a>

            <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
                <i class="ki-outline ki-black-left-line fs-3 rotate-180 text-white"></i>
            </div>
            <!--end::Sidebar toggle-->
        </div>
        @endenv

        <!--end::Logo-->
        <!--begin::sidebar menu-->
        <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
            <!--begin::Menu wrapper-->
            <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
                <!--begin::Scroll wrapper-->
                <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
                    <!--begin::Menu-->
                    <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                        <div class="col-inner-top col-sm-12">
                            <div class="cnCol">
                                <p class="top">{{ trans('global.hello') }},</p>
                                <p class="name">
                                    {{ Auth::user()->name }}
                                </p>
                                <p class="title">
                                    {{ Auth::user()->roles->first()->title }}
                                </p>
                            </div>
                            <div class="cnCol">
                                @php
                                    use Carbon\Carbon;

                                    $currentDate = Carbon::now();
                                    $dayName = $currentDate->format('l');
                                    $dayNumber = $currentDate->format('d');
                                    $monthYear = $currentDate->format('F Y');
                                @endphp

                                <p class="day">{{ strtoupper($dayName) }}</p>
                                <p class="date">{{ $dayNumber }}</p>
                                <p class="month">{{ strtoupper($monthYear) }}</p>
                            </div>

                        </div>
                        <!--begin:Menu item-->
                        <div class="menu-item pt-5">
                            <!--begin:Menu content-->
                            <div class="menu-content menu-dash">
                                <a href="{{ route('admin.home') }}"><span class="menu-heading fw-bold text-uppercase fs-7">Dashboard</span></a>
                            </div>
                            <!--end:Menu content-->
                        </div>
                        <!--end:Menu item-->

                        <!--begin:User Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/permissions*') ? 'show' : '' }} {{ request()->is('admin/roles*') ? 'show' : '' }} {{ request()->is('admin/users*') ? 'show' : '' }} {{ request()->is('admin/audit-logs*') ? 'show' : '' }} {{ request()->is('admin/teams*') ? 'show' : '' }}">

                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-users"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.userManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>

                            <div class="menu-sub menu-sub-accordion">

                                @can('permission_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/permissions') || request()->is('admin/permissions/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.permissions.index') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">{{ trans('cruds.permission.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('role_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/roles') || request()->is('admin/roles/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.roles.index') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">{{ trans('cruds.role.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('user_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/users') || request()->is('admin/users/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.users.index') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">{{ trans('cruds.user.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('audit_log_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/audit-logs') || request()->is('admin/audit-logs/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.audit-logs.index') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">{{ trans('cruds.auditLog.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('team_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/teams') || request()->is('admin/teams/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.teams.index') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">{{ trans('cruds.team.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                            </div>
                        </div>
                        <!--end:User Management-->

                        <!--begin:Provider Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/providers*') ? 'show' : '' }} {{ request()->is('admin/provider-catergories*') ? 'show' : '' }} {{ request()->is('admin/provider-locations*') ? 'show' : '' }} {{ request()->is('admin/provider-types*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-building"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.providerManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('provider_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/providers') || request()->is('admin/providers/*') ? 'active' : '' }}" href="{{ route('admin.providers.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.provider.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('provider_catergory_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/provider-catergories') || request()->is('admin/provider-catergories/*') ? 'active' : '' }}" href="{{ route('admin.provider-catergories.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.providerCatergory.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('provider_location_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/provider-locations') || request()->is('admin/provider-locations/*') ? 'active' : '' }}" href="{{ route('admin.provider-locations.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.providerLocation.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('provider_type_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/provider-types') || request()->is('admin/provider-types/*') ? 'active' : '' }}" href="{{ route('admin.provider-types.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.providerType.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Provider Management-->

                        <!--begin:Package Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/package-types*') ? 'show' : '' }} {{ request()->is('admin/package-types*') ? 'show' : '' }} {{ request()->is('admin/package-locations*') ? 'show' : '' }} {{ request()->is('admin/package-schedules*') ? 'show' : '' }} {{ request()->is('admin/package-prices*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-box"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.packageManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('package_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/packages') || request()->is('admin/packages/*') ? 'active' : '' }}" href="{{ route('admin.packages.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.package.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('package_type_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/package-types') || request()->is('admin/package-types/*') ? 'active' : '' }}" href="{{ route('admin.package-types.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.packageType.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('package_location_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/package-locations') || request()->is('admin/package-locations/*') ? 'active' : '' }}" href="{{ route('admin.package-locations.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.packageLocation.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('package_schedule_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/package-schedules') || request()->is('admin/package-schedules/*') ? 'active' : '' }}" href="{{ route('admin.package-schedules.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.packageSchedule.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('package_price_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/package-prices') || request()->is('admin/package-prices/*') ? 'active' : '' }}" href="{{ route('admin.package-prices.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.packagePrice.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Package Management-->

                        <!--begin:Customer Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/customers*') ? 'show' : '' }} {{ request()->is('admin/customer-addresses*') ? 'show' : '' }} {{ request()->is('admin/customer-emails*') ? 'show' : '' }} {{ request()->is('admin/customer-details*') ? 'show' : '' }} {{ request()->is('admin/customer-passes*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-user-group"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.customerManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('customer_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customers') || request()->is('admin/customers/*') ? 'active' : '' }}" href="{{ route('admin.customers.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customer.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('customer_address_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-addresses') || request()->is('admin/customer-addresses/*') ? 'active' : '' }}" href="{{ route('admin.customer-addresses.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerAddress.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('customer_email_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-emails') || request()->is('admin/customer-emails/*') ? 'active' : '' }}" href="{{ route('admin.customer-emails.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerEmail.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('customer_detail_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-details') || request()->is('admin/customer-details/*') ? 'active' : '' }}" href="{{ route('admin.customer-details.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerDetail.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                {{-- @can('customer_group_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-groups') || request()->is('admin/customer-groups/*') ? 'active' : '' }}" href="{{ route('admin.customer-groups.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerGroup.title') }}</span>
                                        </a>
                                    </div>
                                @endcan --}}

                                @can('customer_pass_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-passes') || request()->is('admin/customer-passes/*') ? 'active' : '' }}" href="{{ route('admin.customer-passes.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerPass.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Customer Management-->

                        <!--begin:Issuer Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/issuers*') ? 'show' : '' }} {{ request()->is('admin/issuer-catergories*') ? 'show' : '' }} {{ request()->is('admin/issuer-locations*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-id-card"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.issuerManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('issuer_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/issuers') || request()->is('admin/issuers/*') ? 'active' : '' }}" href="{{ route('admin.issuers.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.issuer.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('issuer_catergory_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/issuer-catergories') || request()->is('admin/issuer-catergories/*') ? 'active' : '' }}" href="{{ route('admin.issuer-catergories.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.issuerCatergory.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('issuer_location_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/issuer-locations') || request()->is('admin/issuer-locations/*') ? 'active' : '' }}" href="{{ route('admin.issuer-locations.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.issuerLocation.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Issuer Management-->

                        <!--begin:Guide Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/guides*') ? 'show' : '' }} {{ request()->is('admin/guide-categories*') ? 'show' : '' }} {{ request()->is('admin/guide-questions*') ? 'show' : '' }} {{ request()->is('admin/guide-question-answers*') ? 'show' : '' }} {{ request()->is('admin/guide-customer-progresss*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-map"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.guideManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('guide_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/guides') || request()->is('admin/guides/*') ? 'active' : '' }}" href="{{ route('admin.guides.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.guide.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('guide_catergory_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/guide-categories') || request()->is('admin/guide-categories/*') ? 'active' : '' }}" href="{{ route('admin.guide-categories.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.guideCategory.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('guide_question_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/guide-questions') || request()->is('admin/guide-questions/*') ? 'active' : '' }}" href="{{ route('admin.guide-questions.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.guideQuestion.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('guide_question_answer_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/guide-question-answers') || request()->is('admin/guide-question-answers/*') ? 'active' : '' }}" href="{{ route('admin.guide-question-answers.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.guideQuestionAnswer.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('guide_customer_progress_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/guide-customer-progresss') || request()->is('admin/guide-customer-progresss/*') ? 'active' : '' }}" href="{{ route('admin.guide-customer-progresss.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.guideCustomerProgress.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Guide Management-->

                        <!--begin:Order Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/orders*') ? 'show' : '' }} {{ request()->is('admin/order-items*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-cart-shopping"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.orderManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('order_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/orders') || request()->is('admin/orders/*') ? 'active' : '' }}" href="{{ route('admin.orders.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.order.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('order_item_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/order-items') || request()->is('admin/order-items/*') ? 'active' : '' }}" href="{{ route('admin.order-items.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.orderItem.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Order Management-->

                        <!--begin:Pass Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/pass-types*') ? 'show' : '' }} {{ request()->is('admin/pass-add-ons*') ? 'show' : '' }} {{ request()->is('admin/passes*') ? 'show' : '' }} {{ request()->is('admin/pass-add-on-types*') ? 'show' : '' }} {{ request()->is('admin/pass-statuses*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-ticket"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.passManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('pass_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/passes') || request()->is('admin/passes/*') ? 'active' : '' }}" href="{{ route('admin.passes.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.pass.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('pass_type_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/pass-types') || request()->is('admin/pass-types/*') ? 'active' : '' }}" href="{{ route('admin.pass-types.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.passType.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('pass_add_on_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/pass-add-ons') || request()->is('admin/pass-add-ons/*') ? 'active' : '' }}" href="{{ route('admin.pass-add-ons.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.passAddOn.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('pass_add_on_type_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/pass-add-on-types') || request()->is('admin/pass-add-on-types/*') ? 'active' : '' }}" href="{{ route('admin.pass-add-on-types.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.passAddOnType.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('pass_status_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/pass-statuses') || request()->is('admin/pass-statuses/*') ? 'active' : '' }}" href="{{ route('admin.pass-statuses.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.passStatus.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Pass Management-->

                        <!--begin:Voucher Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/voucher-statuses*') ? 'show' : '' }} {{ request()->is('admin/voucher-types*') ? 'show' : '' }} {{ request()->is('admin/vouchers*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-gift"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.voucherManagement.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('voucher_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/vouchers') || request()->is('admin/vouchers/*') ? 'active' : '' }}" href="{{ route('admin.vouchers.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.voucher.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('voucher_status_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/voucher-statuses') || request()->is('admin/voucher-statuses/*') ? 'active' : '' }}" href="{{ route('admin.voucher-statuses.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.voucherStatus.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('voucher_type_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/voucher-types') || request()->is('admin/voucher-types/*') ? 'active' : '' }}" href="{{ route('admin.voucher-types.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.voucherType.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                            </div>
                        </div>
                        <!--end:Voucher Management-->

                        <!--begin:Media Library-->
                        @can('media_library_access')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/media-library') || request()->is('admin/media-library/*') ? 'active' : '' }}" href="{{ route('admin.media-library.index') }}">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-images"></i>
                                    </span>
                                    <span class="menu-title">{{ trans('cruds.mediaLibrary.title') }}</span>
                                </a>
                            </div>
                        @endcan
                        <!--end:Media Library-->

                        <!--begin:Setting Management-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/customer-groups*') ? 'show' : '' }} {{ request()->is('admin/settings*') ? 'show' : '' }} {{ request()->is("admin/markets*") ? "show" : "" }} {{ request()->is('admin/currencies*') ? 'show' : '' }} {{ request()->is('admin/countries*') ? 'show' : '' }} {{ request()->is('admin/day-of-weeks*') ? 'show' : '' }} {{ request()->is('admin/seasons*') ? 'show' : '' }} {{ request()->is('admin/age-groups*') ? 'show' : '' }} {{ request()->is('admin/genders*') ? 'show' : '' }} {{ request()->is('admin/payment-methods*') ? 'show' : '' }} {{ request()->is('admin/vat-percentages*') ? 'show' : '' }} {{ request()->is('admin/banks*') ? 'show' : '' }}">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="fa-solid fa-cogs"></i>
                                </span>
                                <span class="menu-title">{{ trans('cruds.setting.title') }}</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                @can('setting_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/settings') || request()->is('admin/settings/*') ? 'active' : '' }}" href="{{ route('admin.settings.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.setting.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('bank_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/banks') || request()->is('admin/banks/*') ? 'active' : '' }}" href="{{ route('admin.banks.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.bank.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('customer_group_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/customer-groups') || request()->is('admin/customer-groups/*') ? 'active' : '' }}" href="{{ route('admin.customer-groups.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.customerGroup.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('market_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/markets') || request()->is('admin/markets/*') ? 'active' : '' }}" href="{{ route('admin.markets.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.market.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('currency_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/currencies') || request()->is('admin/currencies/*') ? 'active' : '' }}" href="{{ route('admin.currencies.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.currency.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('country_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/countries') || request()->is('admin/countries/*') ? 'active' : '' }}" href="{{ route('admin.countries.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.country.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('day_of_week_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/day-of-weeks') || request()->is('admin/day-of-weeks/*') ? 'active' : '' }}" href="{{ route('admin.day-of-weeks.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.dayOfWeek.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('season_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/seasons') || request()->is('admin/seasons/*') ? 'active' : '' }}" href="{{ route('admin.seasons.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.season.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('age_group_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/age-groups') || request()->is('admin/age-groups/*') ? 'active' : '' }}" href="{{ route('admin.age-groups.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.ageGroup.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('gender_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/genders') || request()->is('admin/genders/*') ? 'active' : '' }}" href="{{ route('admin.genders.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.gender.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('payment_method_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/payment-methods') || request()->is('admin/payment-methods/*') ? 'active' : '' }}" href="{{ route('admin.payment-methods.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.paymentMethod.title') }}</span>
                                        </a>
                                    </div>
                                @endcan

                                @can('vat_percentage_access')
                                    <div class="menu-item">
                                        <a class="menu-link {{ request()->is('admin/vat-percentages') || request()->is('admin/vat-percentages/*') ? 'active' : '' }}" href="{{ route('admin.vat-percentages.index') }}">
                                            <span class="menu-bullet"><span class="bullet bullet-dot"></span></span>
                                            <span class="menu-title">{{ trans('cruds.vatPercentage.title') }}</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <!--end:Setting Management-->

                    </div>
                    <!--end::Menu-->
                </div>
                <!--end::Scroll wrapper-->
            </div>
            <!--end::Menu wrapper-->
        </div>
        <!--end::sidebar menu-->
</div>
