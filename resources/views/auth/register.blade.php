@extends('layouts.app')

@section('styles')
<style>
    .login-container {
        min-height: 100vh;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-header {
        text-align: center;
        margin-bottom: 2.5rem;
    }

    .login-title {
        color: #2d3748;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
    }

    .login-subtitle {
        color: #718096;
        font-size: 1rem;
        font-weight: 400;
    }

    .form-group {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label {
        display: block;
        color: #4a5568;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        letter-spacing: 0.025em;
    }

    .form-input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: #ffffff;
        color: #2d3748;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .form-input.is-invalid {
        border-color: #e53e3e;
    }

    .invalid-feedback {
        color: #e53e3e;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: block;
    }

    .login-button {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.875rem 1.5rem;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .login-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .login-button:active {
        transform: translateY(0);
    }

    .links-container {
        text-align: center;
        border-top: 1px solid #e2e8f0;
        padding-top: 1.5rem;
    }

    .auth-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.2s ease;
        margin: 0 0.5rem;
    }

    .auth-link:hover {
        color: #764ba2;
        text-decoration: none;
    }

    .alert {
        border-radius: 12px;
        border: none;
        padding: 1rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
    }

    .alert-success {
        background: rgba(72, 187, 120, 0.1);
        color: #48bb78;
        border-left: 4px solid #48bb78;
    }

    /* Responsive */
    @media (max-width: 576px) {
        .login-card {
            margin: 1rem;
            padding: 2rem;
        }
        
        .login-title {
            font-size: 1.75rem;
        }
    }
</style>
@endsection

@section('content')
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="login-title">{{ trans('panel.site_title') }}</h1>
            <p class="login-subtitle">{{ trans('global.register') }}</p>
        </div>

        <form method="POST" action="{{ route('register') }}">
            @csrf
            @if(request()->has('team'))
                <input type="hidden" name="team" id="team" value="{{ request()->query('team') }}">
            @endif

            <div class="form-group">
                <label for="name" class="form-label">{{ trans('global.user_name') }}</label>
                <input 
                    id="name" 
                    name="name" 
                    type="text" 
                    class="form-input{{ $errors->has('name') ? ' is-invalid' : '' }}" 
                    required 
                    autofocus 
                    placeholder="Enter your full name"
                    value="{{ old('name', null) }}"
                >
                @if($errors->has('name'))
                    <div class="invalid-feedback">
                        {{ $errors->first('name') }}
                    </div>
                @endif
            </div>

            <div class="form-group">
                <label for="email" class="form-label">{{ trans('global.login_email') }}</label>
                <input 
                    id="email" 
                    name="email" 
                    type="email" 
                    class="form-input{{ $errors->has('email') ? ' is-invalid' : '' }}" 
                    required 
                    placeholder="Enter your email address"
                    value="{{ old('email', null) }}"
                >
                @if($errors->has('email'))
                    <div class="invalid-feedback">
                        {{ $errors->first('email') }}
                    </div>
                @endif
            </div>

            <div class="form-group">
                <label for="password" class="form-label">{{ trans('global.login_password') }}</label>
                <input 
                    id="password" 
                    name="password" 
                    type="password" 
                    class="form-input{{ $errors->has('password') ? ' is-invalid' : '' }}" 
                    required 
                    placeholder="Enter your password"
                >
                @if($errors->has('password'))
                    <div class="invalid-feedback">
                        {{ $errors->first('password') }}
                    </div>
                @endif
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">{{ trans('global.login_password_confirmation') }}</label>
                <input 
                    id="password_confirmation" 
                    name="password_confirmation" 
                    type="password" 
                    class="form-input" 
                    required 
                    placeholder="Confirm your password"
                >
            </div>

            <button type="submit" class="login-button">
                {{ trans('global.register') }}
            </button>

            <div class="links-container">
                <a class="auth-link" href="{{ route('login') }}">
                    Already have an account? Sign in
                </a>
            </div>
        </form>
    </div>
</div>
@endsection