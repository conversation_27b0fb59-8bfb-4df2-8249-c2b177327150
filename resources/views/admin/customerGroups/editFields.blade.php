<div class="form-group mb-3">
    <label class="form-label" for="name">{{ trans('cruds.customerGroup.fields.name') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $customerGroup->name) }}">
    @if($errors->has('name'))
        <div class="invalid-feedback">
            {{ $errors->first('name') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerGroup.fields.name_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label">{{ trans('cruds.customerGroup.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\CustomerGroup::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('status', $customerGroup->status) === (string) $key ? 'selected' : '' }}>{{ trans($label) }}</option>
        @endforeach
    </select>
    @if($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerGroup.fields.status_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="description">{{ trans('cruds.customerGroup.fields.description') }}</label>
    <textarea class="form-control {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description">{{ old('description', $customerGroup->description) }}</textarea>
    @if($errors->has('description'))
        <div class="invalid-feedback">
            {{ $errors->first('description') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerGroup.fields.description_helper') }}</span>
</div>