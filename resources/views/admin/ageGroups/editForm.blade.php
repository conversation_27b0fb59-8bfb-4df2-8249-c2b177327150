<div class="form-group mb-3 col-3">
    <label class="form-label" for="name">{{ trans('cruds.ageGroup.fields.name') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $ageGroup->name) }}">
    @if ($errors->has('name'))
        <div class="invalid-feedback">
            {{ $errors->first('name') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.ageGroup.fields.name_helper') }}</span>
</div>
<div class="form-group mb-3 col-3">
    <label class="form-label" for="min_age">{{ trans('cruds.ageGroup.fields.min_age') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('min_age') ? 'is-invalid' : '' }}" type="number" name="min_age" id="min_age" value="{{ old('min_age', $ageGroup->min_age) }}" step="1">
    @if ($errors->has('min_age'))
        <div class="invalid-feedback">
            {{ $errors->first('min_age') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.ageGroup.fields.min_age_helper') }}</span>
</div>
<div class="form-group mb-3 col-3">
    <label class="form-label" for="max_age">{{ trans('cruds.ageGroup.fields.max_age') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('max_age') ? 'is-invalid' : '' }}" type="number" name="max_age" id="max_age" value="{{ old('max_age', $ageGroup->max_age) }}" step="1">
    @if ($errors->has('max_age'))
        <div class="invalid-feedback">
            {{ $errors->first('max_age') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.ageGroup.fields.max_age_helper') }}</span>
</div>

<div class="form-group mb-3 col-3">
    <label class="form-label">{{ trans('cruds.ageGroup.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>
            {{ trans('global.pleaseSelect') }}</option>
        @foreach (App\Models\AgeGroup::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('status', $ageGroup->status) === (string) $key ? 'selected' : '' }}>
                {{ $label }}</option>
        @endforeach
    </select>
    @if ($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.ageGroup.fields.status_helper') }}</span>
</div>
<div class="form-group mb-3 col-12">
    <label class="form-label" for="description">{{ trans('cruds.ageGroup.fields.description') }}</label>
    <textarea class="form-control ckeditor {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description">{!! old('description', $ageGroup->description) !!}</textarea>
    @if ($errors->has('description'))
        <div class="invalid-feedback">
            {{ $errors->first('description') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.ageGroup.fields.description_helper') }}</span>
</div>
