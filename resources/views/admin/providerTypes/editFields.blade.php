<div class="form-group mb-3">
    <label class="form-label" for="name">{{ trans('cruds.providerType.fields.name') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $providerType->name) }}">
    @if($errors->has('name'))
        <div class="invalid-feedback">
            {{ $errors->first('name') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.providerType.fields.name_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label">{{ trans('cruds.providerType.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status_provider_type" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\ProviderType::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('status', $providerType->status) === (string) $key ? 'selected' : '' }}>{{ trans($label) }}</option>
        @endforeach
    </select>
    @if($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.providerType.fields.status_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="description">{{ trans('cruds.providerType.fields.description') }}</label>
    <textarea class="form-control form-control-sm {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description" style="height: 200px">{{ old('description', $providerType->description) }}</textarea>
    @if($errors->has('description'))
        <div class="invalid-feedback">
            {{ $errors->first('description') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.providerType.fields.description_helper') }}</span>
</div>

<script>
    $(document).ready(function() {
        $('#status_provider_type').select2();
    }); 
</script>