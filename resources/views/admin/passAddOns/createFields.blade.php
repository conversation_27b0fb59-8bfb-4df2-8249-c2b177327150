<div class="form-group mb-3 col-12">
    <label class="form-label" for="title">{{ trans('cruds.passAddOn.fields.title') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text"
        name="title" id="title" value="{{ old('title', '') }}">
    @if ($errors->has('title'))
        <div class="invalid-feedback">
            {{ $errors->first('title') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.title_helper') }}</span>
</div>

<div class="form-group mb-3 col-6">
    <label class="form-label" for="logo">{{ trans('cruds.passAddOn.fields.logo') }}</label>
    <div class="needsclick dropzone {{ $errors->has('logo') ? 'is-invalid' : '' }}" id="logo-dropzone">
    </div>
    @if ($errors->has('logo'))
        <div class="invalid-feedback">
            {{ $errors->first('logo') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.logo_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="backgroud_image">{{ trans('cruds.passAddOn.fields.backgroud_image') }}</label>
    <div class="needsclick dropzone {{ $errors->has('backgroud_image') ? 'is-invalid' : '' }}"
        id="backgroud_image-dropzone">
    </div>
    @if ($errors->has('backgroud_image'))
        <div class="invalid-feedback">
            {{ $errors->first('backgroud_image') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.backgroud_image_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="start_date">{{ trans('cruds.passAddOn.fields.start_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('start_date') ? 'is-invalid' : '' }}"
        type="text" name="start_date" id="start_date" value="{{ old('start_date') }}">
    @if ($errors->has('start_date'))
        <div class="invalid-feedback">
            {{ $errors->first('start_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.start_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="end_date">{{ trans('cruds.passAddOn.fields.end_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('end_date') ? 'is-invalid' : '' }}" type="text"
        name="end_date" id="end_date" value="{{ old('end_date') }}">
    @if ($errors->has('end_date'))
        <div class="invalid-feedback">
            {{ $errors->first('end_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.end_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="pass_id">{{ trans('cruds.passAddOn.fields.pass') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('pass') ? 'is-invalid' : '' }}" data-control="select2"
        name="pass_id" id="pass_id">
        @foreach ($passes as $id => $entry)
            <option value="{{ $id }}" {{ old('pass_id') == $id ? 'selected' : '' }}>{{ $entry }}
            </option>
        @endforeach
    </select>
    @if ($errors->has('pass'))
        <div class="invalid-feedback">
            {{ $errors->first('pass') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.pass_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="pass_add_on_type_id">{{ trans('cruds.passAddOn.fields.pass_add_on_type') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('pass_add_on_type') ? 'is-invalid' : '' }}"
        data-control="select2" name="pass_add_on_type_id" id="pass_add_on_type_id">
        @foreach ($pass_add_on_types as $id => $entry)
            <option value="{{ $id }}" {{ old('pass_add_on_type_id') == $id ? 'selected' : '' }}>
                {{ $entry }}</option>
        @endforeach
    </select>
    @if ($errors->has('pass_add_on_type'))
        <div class="invalid-feedback">
            {{ $errors->first('pass_add_on_type') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.pass_add_on_type_helper') }}</span>
</div>
<div class="form-group mb-3 col-12">
    <label class="form-label" for="description">{{ trans('cruds.passAddOn.fields.description') }}</label>
    <textarea class="form-control {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description"
        id="description">{{ old('description') }}</textarea>
    @if ($errors->has('description'))
        <div class="invalid-feedback">
            {{ $errors->first('description') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.passAddOn.fields.description_helper') }}</span>
</div>

@section('scripts')
    <script>
        Dropzone.options.logoDropzone = {
            url: '{{ route('admin.pass-add-ons.storeMedia') }}',
            maxFilesize: 20, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            maxFiles: 1,
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 20,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').find('input[name="logo"]').remove()
                $('form').append('<input type="hidden" name="logo" value="' + response.name + '">')
            },
            removedfile: function(file) {
                file.previewElement.remove()
                if (file.status !== 'error') {
                    $('form').find('input[name="logo"]').remove()
                    this.options.maxFiles = this.options.maxFiles + 1
                }
            },
            init: function() {
                @if (isset($passAddOn) && $passAddOn->logo)
                    var file = {!! json_encode($passAddOn->logo) !!}
                    this.options.addedfile.call(this, file)
                    this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                    file.previewElement.classList.add('dz-complete')
                    $('form').append('<input type="hidden" name="logo" value="' + file.file_name + '">')
                    this.options.maxFiles = this.options.maxFiles - 1
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>

    <script>
        new tempusDominus.TempusDominus(document.getElementById("start_date"), {
            display: {
                viewMode: "calendar",
                components: {
                    decades: true,
                    year: true,
                    month: true,
                    date: true,
                    hours: false,
                    minutes: false,
                    seconds: false
                }
            },
            localization: {
                format: 'yyyy-MM-dd'
            }
        });

         new tempusDominus.TempusDominus(document.getElementById("end_date"), {
            display: {
                viewMode: "calendar",
                components: {
                    decades: true,
                    year: true,
                    month: true,
                    date: true,
                    hours: false,
                    minutes: false,
                    seconds: false
                }
            },
            localization: {
                format: 'yyyy-MM-dd'
            }
        });
    </script>

    <script>
        Dropzone.options.backgroudImageDropzone = {
            url: '{{ route('admin.pass-add-ons.storeMedia') }}',
            maxFilesize: 20, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            maxFiles: 1,
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 20,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').find('input[name="backgroud_image"]').remove()
                $('form').append('<input type="hidden" name="backgroud_image" value="' + response.name + '">')
            },
            removedfile: function(file) {
                file.previewElement.remove()
                if (file.status !== 'error') {
                    $('form').find('input[name="backgroud_image"]').remove()
                    this.options.maxFiles = this.options.maxFiles + 1
                }
            },
            init: function() {
                @if (isset($passAddOn) && $passAddOn->backgroud_image)
                    var file = {!! json_encode($passAddOn->backgroud_image) !!}
                    this.options.addedfile.call(this, file)
                    this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                    file.previewElement.classList.add('dz-complete')
                    $('form').append('<input type="hidden" name="backgroud_image" value="' + file.file_name + '">')
                    this.options.maxFiles = this.options.maxFiles - 1
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>
@endsection
