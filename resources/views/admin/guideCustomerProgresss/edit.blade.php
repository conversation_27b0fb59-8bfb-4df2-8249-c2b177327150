@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.guideCustomerProgress.title') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.guideCustomerProgress.title') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/guide-customer-progresss" class="text-muted text-hover-primary">{{ trans('cruds.guideCustomerProgress.title') }} - </a> {{ trans('global.edit') }} - {{ $guideCustomerProgresss->id }} - {{ $guideCustomerProgresss->customer->first_name ?? '' }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.edit') }} {{ trans('cruds.guideCustomerProgress.title') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" action="{{ route("admin.guide-customer-progresss.update", [$guideCustomerProgresss->id]) }}" enctype="multipart/form-data">
									@method('PUT')
									@csrf
									@include('admin.guideCustomerProgresss.editFields', ['guideCustomerProgress' => $guideCustomerProgresss])
									<div class="form-group mb-3">
										<button class="btn btn-sm btn-danger" type="submit">
											{{ trans('global.save') }}
										</button>
									</div>
								</form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
	$(document).ready(function() {
		new tempusDominus.TempusDominus(document.getElementById("answered_at"), {
			display: {
				viewMode: "calendar",
				components: {
					decades: true,
					year: true,
					month: true,
					date: true,
					hours: true,
					minutes: true,
					seconds: false
				}
			},
			localization: {
				format: 'yyyy-MM-dd HH:mm'
			}
		});
	});
</script>
@endsection
