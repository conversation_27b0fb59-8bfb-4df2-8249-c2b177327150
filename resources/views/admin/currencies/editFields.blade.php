<div class="form-group mb-3">
    <label class="form-label" for="name">{{ trans('cruds.currency.fields.name') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $currency->name) }}">
    @if($errors->has('name'))
        <div class="invalid-feedback">
            {{ $errors->first('name') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.currency.fields.name_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="symbol">{{ trans('cruds.currency.fields.symbol') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('symbol') ? 'is-invalid' : '' }}" type="text" name="symbol" id="symbol" value="{{ old('symbol', $currency->symbol) }}">
    @if($errors->has('symbol'))
        <div class="invalid-feedback">
            {{ $errors->first('symbol') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.currency.fields.symbol_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label">{{ trans('cruds.currency.fields.position') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('position') ? 'is-invalid' : '' }}" name="position" id="position" data-control="select2">
        <option value disabled {{ old('position', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>        @foreach(App\Models\Currency::POSITION_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('position', $currency->position) === (string) $key ? 'selected' : '' }}>{{ trans($label) }}</option>
        @endforeach
    </select>
    @if($errors->has('position'))
        <div class="invalid-feedback">
            {{ $errors->first('position') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.currency.fields.position_helper') }}</span>
</div>