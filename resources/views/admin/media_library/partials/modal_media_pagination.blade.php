@if ($media->hasPages())
    <nav aria-label="Media Library Pagination">
        <ul class="pagination pagination-sm justify-content-center">
            {{-- Previous Page Link --}}
            @if ($media->onFirstPage())
                <li class="page-item disabled"><span class="page-link">Previous</span></li>
            @else
                <li class="page-item"><a class="page-link" href="#" data-page="{{ $media->currentPage() - 1 }}">Previous</a></li>
            @endif

            {{-- Pagination Elements --}}
            @foreach ($media->getUrlRange(1, $media->lastPage()) as $page => $url)
                @if ($page == $media->currentPage())
                    <li class="page-item active"><span class="page-link">{{ $page }}</span></li>
                @else
                    <li class="page-item"><a class="page-link" href="#" data-page="{{ $page }}">{{ $page }}</a></li>
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($media->hasMorePages())
                <li class="page-item"><a class="page-link" href="#" data-page="{{ $media->currentPage() + 1 }}">Next</a></li>
            @else
                <li class="page-item disabled"><span class="page-link">Next</span></li>
            @endif
        </ul>
    </nav>
@endif
