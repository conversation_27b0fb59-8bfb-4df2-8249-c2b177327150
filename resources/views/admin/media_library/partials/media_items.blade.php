@forelse($media as $item)
    <div class="col">
        <div data-image-url="{{ asset('storage/media/' . $item->file_name) }}" class="border rounded p-2 text-center position-relative" style="cursor: pointer;" onclick="copyUrl('{{ asset('storage/media/' . $item->file_name) }}', this)">

            <!-- The hover overlay -->
            <div data-copy-text class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white" style="background-color: rgba(0, 0, 0, 0.5);
                        opacity: 0;
                        transition: opacity 0.2s;">
                Click to copy image url
            </div>

            <img src="{{ asset('storage/media/' . $item->file_name) }}" alt="{{ $item->name }}" class="img-fluid rounded mb-2" style="max-height: 150px; object-fit: cover;">
            <p class="mb-1 small"><strong>{{ $item->name }}</strong></p>
            <p class="mb-1 small text-muted">{{ $item->file_name }}</p>
            <p class="mb-1 small text-muted">{{ number_format($item->size / 1024, 2) }} KB</p>
            <div class="mt-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteMedia({{ $item->id }}, this)">
                    <i class="fa fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
@empty
    <div class="col-12 text-center">
        <p class="text-muted">No images found.</p>
    </div>
@endforelse
