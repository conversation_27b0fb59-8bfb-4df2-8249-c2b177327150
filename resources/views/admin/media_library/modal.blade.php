<!-- Global Media Library Modal -->
<div class="modal fade" id="global_media_library" tabindex="-1" aria-labelledby="mediaLibraryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mediaLibraryModalLabel">{{ trans('cruds.mediaLibrary.title') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <form id="gallery-search-form-modal" method="GET" action="" class="mb-4">
                            @csrf
                            <div class="d-flex gap-2">
                                <input type="text" name="search" id="modal-search-input" placeholder="Search images..." class="form-control form-control-sm flex-grow-1" />
                                <button type="submit" class="btn btn-primary btn-sm">Search</button>
                                <button id="modal-upload-gallery" type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#modalUploadModal">
                                    Upload
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div id="media-library-content" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
                            <!-- Media items will be loaded here via AJAX -->
                            <div class="col-12 text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 d-flex justify-content-center">
                            <nav id="media-pagination">
                                <!-- Pagination will be loaded here -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal for Media Library Modal -->
<div class="modal fade" id="modalUploadModal" tabindex="-1" aria-labelledby="modalUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalUploadModalLabel">Upload Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="modalUploadForm" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="modal_file" class="form-label">Select Image</label>
                        <input type="file" class="form-control" id="modal_file" name="file" accept="image/*" required>
                        <div class="form-text">Maximum file size: 20MB. Supported formats: JPG, PNG, GIF, WebP</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="modalUploadButton">Upload</button>
            </div>
        </div>
    </div>
</div>

<script>
// Global Media Library JavaScript
window.MediaLibrary = {
    currentTargetInput: null,
    
    // Open media library modal
    open: function(targetInputId) {
        this.currentTargetInput = targetInputId;
        $('#global_media_library').modal('show');
        this.loadMedia();
    },
    
    // Load media items
    loadMedia: function(search = '') {
        $.ajax({
            url: "{{ route('admin.media-library.modal') }}",
            type: 'GET',
            data: { 
                search: search,
                ajax: 1 
            },
            success: function(data) {
                $('#media-library-content').html(data.html);
                $('#media-pagination').html(data.pagination);
            },
            error: function(error) {
                console.error('Error loading media:', error);
            }
        });
    },
    
    // Select media for form
    selectMedia: function(url, name) {
        if (this.currentTargetInput) {
            $('#' + this.currentTargetInput).val(url);
            // Trigger change event for any listeners
            $('#' + this.currentTargetInput).trigger('change');
        }
        $('#global_media_library').modal('hide');
    }
};

// Function to select media for form (called from modal items)
function selectMediaForForm(url, name, element) {
    window.MediaLibrary.selectMedia(url, name);
}

// Initialize modal events
$(document).ready(function() {
    // Search functionality
    $('#gallery-search-form-modal').on('submit', function(e) {
        e.preventDefault();
        const search = $('#modal-search-input').val();
        window.MediaLibrary.loadMedia(search);
    });
    
    // Upload functionality
    $('#modalUploadButton').on('click', function() {
        const $form = $('#modalUploadForm');
        const formData = new FormData($form[0]);

        $.ajax({
            url: "{{ route('admin.media-library.upload') }}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(data) {
                if (data.status === 'success') {
                    $('#modalUploadModal').modal('hide');
                    window.MediaLibrary.loadMedia();
                } else {
                    alert('Upload failed: ' + (data.message || 'Unknown error'));
                }
            },
            error: function(error) {
                console.error('Error:', error);
                alert('Upload failed. Please try again.');
            }
        });
    });
    
    // Show/hide overlay on hover for modal items
    $(document).on('mouseenter', '#media-library-content [data-image-url]', function() {
        $(this).find('[data-copy-text]').css('opacity', '1');
    });

    $(document).on('mouseleave', '#media-library-content [data-image-url]', function() {
        $(this).find('[data-copy-text]').css('opacity', '0');
    });
});
</script>
