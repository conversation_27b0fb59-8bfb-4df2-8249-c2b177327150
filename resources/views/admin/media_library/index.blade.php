@extends('layouts.admin')
@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">{{ trans('cruds.mediaLibrary.title_singular') }}</h5>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <form id="gallery-search-form" method="GET" action="{{ route('admin.media-library.index') }}" class="mb-4">
                        @csrf
                        <div class="d-flex gap-2">
                            <input type="text" name="search" placeholder="Search images..." class="form-control form-control-sm flex-grow-1" value="{{ $search }}" />
                            <button type="submit" class="btn btn-primary btn-sm">Search</button>
                            <button id="search-upload-gallery" type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                Upload
                            </button>
                        </div>
                    </form>

                    <div class="row row-cols-1 row-cols-sm-5 row-cols-md-5 row-cols-lg-5 g-4">
                        @forelse($media as $item)
                            <div class="col">
                                <div data-image-url="{{ asset('storage/media/' . $item->file_name) }}" class="border rounded p-2 text-center position-relative" style="cursor: pointer;" onclick="copyUrl('{{ asset('storage/media/' . $item->file_name) }}', this)">

                                    <!-- Delete button positioned at top-right corner -->
                                    <button type="button" class="btn btn-danger btn-sm position-absolute" style="top: 5px; right: 5px; z-index: 10;" onclick="event.stopPropagation(); deleteMedia({{ $item->id }}, this);" title="Delete Image">
                                        <i class="fa fa-trash"></i>
                                    </button>

                                    <!-- The hover overlay -->
                                    <div data-copy-text class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white" style="background-color: rgba(0, 0, 0, 0.5);
                                            opacity: 0;
                                            transition: opacity 0.2s;
                                            z-index: 5;">
                                        Click to copy image url
                                    </div>

                                    <img src="{{ asset('storage/media/' . $item->file_name) }}" alt="{{ $item->name }}" class="img-fluid rounded mb-2" style="max-height: 150px; object-fit: cover;">
                                    <p class="mb-1 small"><strong>{{ $item->name }}</strong></p>
                                    <p class="mb-1 small text-muted">{{ $item->file_name }}</p>
                                    <p class="mb-1 small text-muted">{{ number_format($item->size / 1024, 2) }} KB</p>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <p class="text-muted">No images found.</p>
                            </div>
                        @endforelse
                    </div>

                    <div class="mt-4 d-flex justify-content-center">
                        {{ $media->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- page-loader --}}
    <div class="page-loader flex-column bg-dark bg-opacity-25 m-0">
        <span class="spinner-border text-primary" role="status"></span>
        <span class="text-gray-800 fs-6 fw-semibold mt-5">Loading...</span>
    </div>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            @include('admin.media_library.upload_modal')
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.js"></script>
    <script src="{{ url('/') }}/assets/js/drawer.js"></script>

    <script>
        // Function to copy URL to clipboard
        function copyUrl(url, element) {
            navigator.clipboard.writeText(url).then(function() {
                const $copyText = $(element).find('[data-copy-text]');
                $copyText.text('URL copied!');
                $copyText.css('opacity', '1');

                setTimeout(function() {
                    $copyText.css('opacity', '0');
                    setTimeout(function() {
                        $copyText.text('Click to copy image url');
                    }, 200);
                }, 1000);
            });
        }

        // Function to delete media
        function deleteMedia(mediaId, element) {
            if (confirm('Are you sure you want to delete this image?')) {
                $.ajax({
                    url: "{{ route('admin.media-library.destroy', '') }}/" + mediaId,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(data) {
                        if (data.status === 'success') {
                            $(element).closest('.col').remove();
                        } else {
                            alert('Delete failed: ' + (data.message || 'Unknown error'));
                        }
                    },
                    error: function(error) {
                        console.error('Error:', error);
                        alert('Delete failed. Please try again.');
                    }
                });
            }
        }

        // Handle image upload using jQuery
        $(document).ready(function() {
            $('#uploadButton').on('click', function() {
                const $form = $('#uploadForm');
                const formData = new FormData($form[0]);

                $.ajax({
                    url: "{{ route('admin.media-library.upload') }}",
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(data) {
                        if (data.status === 'success') {
                            // Close modal and refresh page
                            $('#uploadModal').modal('hide');
                            window.location.reload();
                        } else {
                            alert('Upload failed: ' + (data.message || 'Unknown error'));
                        }
                    },
                    error: function(error) {
                        console.error('Error:', error);
                        alert('Upload failed. Please try again.');
                    }
                });
            });

            // Show/hide overlay on hover
            $(document).on('mouseenter', '[data-image-url]', function() {
                $(this).find('[data-copy-text]').css('opacity', '1');
            });

            $(document).on('mouseleave', '[data-image-url]', function() {
                const $copyText = $(this).find('[data-copy-text]');
                if ($copyText.text() !== 'URL copied!') {
                    $copyText.css('opacity', '0');
                }
            });
        });
    </script>
@endsection
