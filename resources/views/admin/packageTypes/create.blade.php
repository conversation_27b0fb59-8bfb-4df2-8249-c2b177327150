@extends ('layouts.admin')

@section('header_title')
    {{ trans('global.create') }} {{ trans('cruds.packageType.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container">
        <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
            <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                {{ trans('cruds.packageType.title_singular') }}
            </h1>
            <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                <li class="breadcrumb-item text-muted">
                    <a href="/admin/package-types" class="text-muted text-hover-primary">{{ trans('cruds.packageType.title_singular') }}</a>
                </li>
                <li class="breadcrumb-item">
                    <span class="bullet bg-gray-500 w-5px h-2px"></span>
                </li>
                <li class="breadcrumb-item text-muted">{{ trans('global.create') }}
                    {{ trans('cruds.packageType.title_singular') }}
                </li>
            </ul>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="row g-0">
    <div class="col-lg-6 col-6 pe-lg-2">
        @include('layouts.common.tab_menu', [
        'tabs' => [
        [
        'option' => 1,
        'name' => trans('global.create'),

        'url' => route('admin.package-types.create'),
        'icon' => '',
        'active' => Route::currentRouteName() == 'admin.package-types.create' ? '1' : '0',],
        ],
        ])
        <div class="card custom-card mb-2 p-4 p-sm-5">

            <div class="card-body p-0">
                <form method="POST" action="{{ route("admin.package-types.store") }}" enctype="multipart/form-data">
                    @csrf
                    @include('admin.packageTypes.createFields')
                    <div class="form-group mb-3">
                        <button class="btn btn-sm btn-danger" type="submit">
                            {{ trans('global.save') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div id="kt_drawer" class="bg-white drawer_crud d-none" data-kt-drawer-activate="true" data-kt-drawer-close="#close_drawer" data-kt-drawer-overlay="true">
    <div class="card w-100" id="drawer_card">
    </div>
</div>
@endsection