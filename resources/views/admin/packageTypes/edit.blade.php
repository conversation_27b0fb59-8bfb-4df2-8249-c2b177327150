@extends ('layouts.admin')

@section('header_title')
{{ trans('global.edit') }} {{ trans('cruds.packageType.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
    <div id="kt_app_toolbar_container">
        <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
            <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                {{ trans('cruds.packageType.title_singular') }}
            </h1>
            <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                <li class="breadcrumb-item text-muted">
                    <a href="/admin/package-types" class="text-muted text-hover-primary">{{ trans('cruds.packageType.title_singular') }} - </a> {{ trans('global.edit') }} - {{ $packageType->id }} - {{ $packageType->title }}
                </li>
            </ul>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="row g-0">
    <div class="col-lg-6 col-6 pe-lg-2">
        <div class="card custom-card mb-2 p-4 p-sm-5">
            <div class="card-body p-0">
                <form method="POST" action="{{ route("admin.package-types.update", [$packageType->id]) }}" enctype="multipart/form-data">
                    @method('PUT')
                    @csrf
                    @include('admin.packageTypes.editFields')
                    <div class="form-group">
                        <button class="btn btn-danger" type="submit">
                            {{ trans('global.save') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection