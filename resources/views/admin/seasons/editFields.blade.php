<div class="form-group mb-3 col-6">
    <label class="form-label" for="name">{{ trans('cruds.season.fields.name') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text"
        name="name" id="name" value="{{ old('name', $season->name) }}">
    @if ($errors->has('name'))
        <div class="invalid-feedback">
            {{ $errors->first('name') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.season.fields.name_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label">{{ trans('cruds.season.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>
            {{ trans('global.pleaseSelect') }}</option>
        @foreach (App\Models\Season::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}"
                {{ old('status', $season->status) === (string) $key ? 'selected' : '' }}>
                {{ $label }}</option>
        @endforeach
    </select>
    @if ($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.season.fields.status_helper') }}</span>
</div>

<div class="form-group mb-3 col-6">
    <label class="form-label" for="start_date">{{ trans('cruds.season.fields.start_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('start_date') ? 'is-invalid' : '' }}"
        type="text" name="start_date" id="start_date" value="{{ old('start_date', $season->start_date) }}">
    @if ($errors->has('start_date'))
        <div class="invalid-feedback">
            {{ $errors->first('start_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.season.fields.start_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="end_date">{{ trans('cruds.season.fields.end_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('end_date') ? 'is-invalid' : '' }}" type="text"
        name="end_date" id="end_date" value="{{ old('end_date', $season->end_date) }}">
    @if ($errors->has('end_date'))
        <div class="invalid-feedback">
            {{ $errors->first('end_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.season.fields.end_date_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="description">{{ trans('cruds.season.fields.description') }}</label>
    <textarea class="form-control ckeditor {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description"
        id="description">{!! old('description', $season->description) !!}</textarea>
    @if ($errors->has('description'))
        <div class="invalid-feedback">
            {{ $errors->first('description') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.season.fields.description_helper') }}</span>
</div>