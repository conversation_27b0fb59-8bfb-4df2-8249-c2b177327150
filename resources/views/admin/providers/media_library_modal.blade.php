<!-- Media Library Modal -->
<div class="modal fade" id="provider_media_library" tabindex="-1" aria-labelledby="mediaLibraryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mediaLibraryModalLabel">Provider Media Library</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <form id="gallery-search-form-modal" method="GET" action="" class="mb-4">
                            @csrf
                            <div class="d-flex gap-2">
                                <input type="text" name="search" id="modal-search-input" placeholder="Search images..." class="form-control form-control-sm flex-grow-1" />
                                <button id="search-button-gallery-modal" type="button" class="btn btn-primary btn-sm">
                                    Search
                                </button>
                                <button id="search-upload-gallery-modal" type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModalInner">
                                    Upload
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div id="media-library-content" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
                            <!-- Media items will be loaded here via AJAX -->
                            <div class="col-12 text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 d-flex justify-content-center">
                            <nav id="media-pagination">
                                <!-- Pagination will be loaded here -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal (Inner) -->
<div class="modal fade" id="uploadModalInner" tabindex="-1" aria-labelledby="uploadModalInnerLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalInnerLabel">Upload Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="uploadFormModal" enctype="multipart/form-data">
                    @csrf
                    <div class="mb-3">
                        <label for="image_type_modal" class="form-label">Image Type</label>
                        <select class="form-select form-select-sm" id="image_type_modal" name="image_type" required data-control="select2" data-placeholder="Select Image Type" data-allow-clear="true" data-dropdown-parent="#uploadModalInner">
                            <option value="">Select Image Type</option>
                            @foreach(App\Models\Provider::IMAGE_TYPES as $key => $value)
                                <option value="{{ $key }}">{{ $value }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="file_modal" class="form-label">Image File</label>
                        <input type="file" class="form-control form-control-sm" id="file_modal" name="file" accept="image/*" required>
                        <div class="form-text">Max file size: 20MB</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="uploadButtonModal">Upload</button>
            </div>
        </div>
    </div>
</div>
