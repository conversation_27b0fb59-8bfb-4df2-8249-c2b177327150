@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.provider.title') }}
@endsection

@section('styles')
<style>
    .page-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }

    .page-loader.show {
        opacity: 1;
        visibility: visible;
    }
</style>
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center mt-4 my-0">
                    {{ trans('cruds.provider.title') }} Media Library
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/providers" class="text-muted text-hover-primary">{{ trans('cruds.provider.title') }} </a> - {{ trans('global.edit') }} {{ trans('cruds.provider.title') }} Media Library - {{ $provider->id }} - {{ $provider->name }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
<div class="row g-0">
    <div class="col-lg-12 col-12 pe-lg-2">

        @include('admin.providers.provider_tabs', ['provider' => $provider])

        <div class="card custom-card mb-2 p-4 p-sm-5">
            <div class="card-body p-0">
                <div class="col-lg-12 pe-lg-2">
                    <div class="card mb-2 p-4 p-sm-5">
                        <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                            <h4>Media Library</h4>
                        </div>
                        <div class="card-body p-0">
                            <form id="gallery-search-form" method="GET" action="" class="mb-4">
                                @csrf
                                <div class="d-flex gap-2">
                                    <input type="text" name="search" value="{{ $search ?? '' }}" placeholder="Search images..." class="form-control form-control-sm flex-grow-1" />

                                    <button id="search-button-gallery" type="submit" class="btn btn-primary btn-sm">
                                        Search
                                    </button>

                                    <button id="search-upload-gallery" type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                        Upload
                                    </button>
                                </div>
                            </form>

                            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
                                @forelse($media as $item)
                                    <div class="col">
                                        <div data-image-url="{{ $item->getUrl() }}" class="border rounded p-2 text-center position-relative" style="cursor: pointer;" onclick="copyUrl('{{ $item->getUrl() }}', this)">

                                            <!-- The hover overlay -->
                                            <div data-copy-text class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white" style="background-color: rgba(0, 0, 0, 0.5);
                                                        opacity: 0;
                                                        transition: opacity 0.2s;">
                                                Click to copy image url
                                            </div>

                                            <img src="{{ $item->getUrl('thumb') ?? $item->getUrl() }}" alt="{{ $item->name }}" class="w-100 mb-2" style="height: 8rem; object-fit: cover;" />

                                            <div class="fw-semibold small text-truncate">
                                                {{ $item->name }}
                                            </div>
                                            @if($item->image_type)
                                                <div class="badge bg-primary mb-1">
                                                    {{ App\Models\Provider::IMAGE_TYPES[$item->image_type] ?? $item->image_type }}
                                                </div>
                                            @endif
                                            <div class="text-muted small text-truncate">
                                                {{ $item->getUrl() }}
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12 text-muted mt-10 fs-4">
                                        No images found.
                                    </div>
                                @endforelse
                            </div>

                            <div class="mt-4">
                                {{ $media->links('pagination::bootstrap-5') }}
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- page-loader --}}
<div class="page-loader flex-column bg-dark bg-opacity-25 m-0">
    <span class="spinner-border text-primary" role="status"></span>
    <span class="text-gray-800 fs-6 fw-semibold mt-5">Loading...</span>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        @include('layouts.common.media_library_upload')
    </div>
</div>

@endsection

@section('scripts')
<script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.js"></script>
<script src="{{ url('/') }}/assets/js/drawer.js"></script>


<script>
    // Function to copy URL to clipboard
    function copyUrl(url, element) {
        navigator.clipboard.writeText(url).then(function() {
            const $copyText = $(element).find('[data-copy-text]');
            $copyText.text('URL copied!');
            $copyText.css('opacity', '1');

            setTimeout(function() {
                $copyText.css('opacity', '0');
                setTimeout(function() {
                    $copyText.text('Click to copy image url');
                }, 200);
            }, 1000);
        });
    }

    // Handle image upload using jQuery
    $(document).ready(function() {
        $('#uploadButton').on('click', function() {
            const $form = $('#uploadForm');
            const formData = new FormData($form[0]);

            $.ajax({
                url: "{{ route('admin.providers.mediaLibraryUpload', $provider->id ) }}",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(data) {
                    if (data.status === 'success') {
                        // Close modal and refresh page
                        $('#uploadModal').modal('hide');
                        window.location.reload();
                    } else {
                        alert('Upload failed: ' + (data.message || 'Unknown error'));
                    }
                },
                error: function(error) {
                    console.error('Error:', error);
                    alert('Upload failed. Please try again.');
                }
            });
        });

        // Show hover effect on image cards
        $('[data-image-url]').each(function() {
            const $card = $(this);
            const $overlay = $card.find('[data-copy-text]');

            $card.on('mouseenter', function() {
                $overlay.css('opacity', '1');
            });

            $card.on('mouseleave', function() {
                $overlay.css('opacity', '0');
            });
        });
    });
</script>
@endsection
