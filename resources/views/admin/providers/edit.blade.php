@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.provider.title') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center mt-4 my-0">
                    {{ trans('cruds.provider.title') }} Details
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/providers" class="text-muted text-hover-primary">{{ trans('cruds.provider.title') }} </a> -  {{ trans('global.edit') }} - {{ $provider->id }} - {{ $provider->name }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="row g-0">
        <div class="col-lg-12 col-12 pe-lg-2">

            @include('admin.providers.provider_tabs', ['provider' => $provider])

            <div class="card custom-card mb-2 p-4 p-sm-5">
                <div class="card-body p-0">
                    <form class="row" method="POST" action="{{ route('admin.providers.update', [$provider->id]) }}" enctype="multipart/form-data">
                        @method('PUT')
                        @csrf
                        <div class="row">

                            <div class="form-group mb-3 col-12">
                                <label class="form-label" for="name">{{ trans('cruds.provider.fields.name') }}</label>
                                <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $provider->name) }}">
                                @if ($errors->has('name'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('name') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.name_helper') }}</span>
                            </div>

                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="logo">{{ trans('cruds.provider.fields.logo') }}</label>
                                <div class="input-group input-group-solid flex-nowrap">
                                    <div class="overflow-hidden flex-grow-1">
                                        <input class="form-control form-control-sm rounded-end-0 {{ $errors->has('logo') ? 'is-invalid' : '' }}" type="text" name="logo" id="logo" value="{{ old('logo', $provider->logo ? $provider->logo->getUrl() : '') }}" placeholder="Select logo from media library">
                                    </div>
                                    <button type="button" class="btn btn-sm btn-primary" onclick="openMediaLibrary('logo')" title="Select from Media Library">
                                        <i class="fa-solid fa-images"></i>
                                    </button>
                                </div>
                                @if ($errors->has('logo'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('logo') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.logo_helper') }}</span>
                                <!-- Logo Preview -->
                                <div class="mt-2">
                                    <img id="logo_preview" src="{{ old('logo', $provider->logo ? $provider->logo->getUrl() : '') }}" alt="Logo Preview" style="max-width: 100px; max-height: 50px; object-fit: contain; {{ old('logo', $provider->logo ? $provider->logo->getUrl() : '') ? '' : 'display: none;' }}" class="border rounded">
                                </div>
                            </div>

                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="provider_type_id">{{ trans('cruds.provider.fields.provider_type') }}</label>
                                <div class="input-group input-group-solid flex-nowrap">
                                    <div class="overflow-hidden flex-grow-1">
                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('provider_type_id') ? 'is-invalid' : '' }}" name="provider_type_id" id="provider_type_id" data-control="select2" data-allow-clear="true" data-placeholder="Please select...">
                                            @foreach ($provider_types as $id => $entry)
                                                <option value="{{ $id }}" {{ (old('provider_type_id') ? old('provider_type_id') : $provider->provider_type->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    @canany(['provider_type_create', 'provider_type_edit'])
                                        <span class="btn btn-sm btn-primary" id="drawer_provider_type" data-action="add" data-select="provider_type_id" data-target-id="">
                                            <i class="fa-solid fa-add"></i>
                                        </span>
                                    @endcanany
                                </div>

                                @if ($errors->has('provider_type'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('provider_type') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.provider_type_helper') }}</span>
                            </div>

                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="phone">{{ trans('cruds.provider.fields.phone') }}</label>
                                <div class="input-group input-group-sm mb-5">
                                    <span class="input-group-text p-0" id="inputGroup-sizing-sm">
                                        <select class="form-select form-select-sm form-select-transparent py-0 w-100 {{ $errors->has('phone_code_id') ? 'is-invalid' : '' }}" name="phone_code_id" id="kt_docs_select2_country">
                                            @foreach ($phone_codes as $item_phone_code)
                                                <option value="{{ $item_phone_code->id }}" data-kt-select2-country="{{ url('/') }}/assets/media/flags/{{ $item_phone_code->flag }}" {{ (old('phone_code_id') ? old('phone_code_id') : $provider->phone_code_id) == $item_phone_code->id ? 'selected' : '' }}>
                                                    {{ $item_phone_code->phone_code }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </span>
                                    <input class="form-control form-control-sm {{ $errors->has('phone') ? 'is-invalid' : '' }}" type="text" name="phone" id="phone" value="{{ old('phone', $provider->phone) }}">
                                </div>
                            </div>
                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="telephone">{{ trans('cruds.provider.fields.telephone') }}</label>
                                <input class="form-control form-control-sm {{ $errors->has('telephone') ? 'is-invalid' : '' }}" type="text" name="telephone" id="telephone" value="{{ old('telephone', $provider->telephone) }}">
                                @if ($errors->has('telephone'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('telephone') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.telephone_helper') }}</span>
                            </div>
                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="email">{{ trans('cruds.provider.fields.email') }}</label>
                                <input class="form-control form-control-sm {{ $errors->has('email') ? 'is-invalid' : '' }}" type="email" name="email" id="email" value="{{ old('email', $provider->email) }}">
                                @if ($errors->has('email'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('email') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.email_helper') }}</span>
                            </div>


                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="brn">{{ trans('cruds.provider.fields.brn') }}</label>
                                <input class="form-control form-control-sm {{ $errors->has('brn') ? 'is-invalid' : '' }}" type="text" name="brn" id="brn" value="{{ old('brn', $provider->brn) }}">
                                @if ($errors->has('brn'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('brn') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.brn_helper') }}</span>
                            </div>

                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="vat_number">{{ trans('cruds.provider.fields.vat_number') }}</label>
                                <input class="form-control form-control-sm {{ $errors->has('vat_number') ? 'is-invalid' : '' }}" type="text" name="vat_number" id="vat_number" value="{{ old('vat_number', $provider->vat_number) }}">
                                @if ($errors->has('vat_number'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('vat_number') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.vat_number_helper') }}</span>
                            </div>

                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="provider_catergory_id">{{ trans('cruds.provider.fields.provider_catergory') }}</label>
                                <div class="input-group input-group-solid flex-nowrap">
                                    <div class="overflow-hidden flex-grow-1">
                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('provider_catergory') ? 'is-invalid' : '' }}" name="provider_catergory_id" id="provider_catergory_id" data-control="select2">
                                            @foreach ($provider_catergories as $id => $entry)
                                                <option value="{{ $id }}" {{ (old('provider_catergory_id') ? old('provider_catergory_id') : $provider->provider_catergory->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    @canany(['provider_catergory_create', 'provider_catergory_edit'])
                                        <span class="btn btn-sm btn-primary" id="drawer_provider_catergory" data-action="add" data-select="provider_catergory_id" data-target-id="">
                                            <i class="fa-solid fa-add"></i>
                                        </span>
                                    @endcanany
                                </div>
                                @if ($errors->has('provider_catergory'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('provider_catergory') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.provider_catergory_helper') }}</span>
                            </div>
                            <div class="form-group mb-3 col-3">
                                <label class="form-label" for="market_id">{{ trans('cruds.provider.fields.market') }}</label>
                                <div class="input-group input-group-solid flex-nowrap">
                                    <div class="overflow-hidden flex-grow-1">
                                        <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('market') ? 'is-invalid' : '' }}" name="market_id" id="market_id" data-control="select2">
                                            @foreach ($markets as $id => $entry)
                                                <option value="{{ $id }}" {{ (old('market_id') ? old('market_id') : $provider->market->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    @canany(['market_create', 'market_edit'])
                                        <span class="btn btn-sm btn-primary" id="drawer_market" data-action="add" data-select="market_id" data-target-id="">
                                            <i class="fa-solid fa-add"></i>
                                        </span>
                                    @endcanany
                                </div>
                                @if ($errors->has('market'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('market') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.market_helper') }}</span>
                            </div>
							<div class="form-group mb-3 col-3">
                                <label class="form-label" for="status">{{ trans('cruds.provider.fields.status') }}</label>
                                <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
                                    <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
                                    @foreach (App\Models\Provider::STATUS_SELECT as $key => $label)
                                        <option value="{{ $key }}" {{ old('status', $provider->status) === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
                                    @endforeach
                                </select>
                                @if ($errors->has('status'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('status') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.status_helper') }}</span>
                            </div>
                            {{-- <div class="form-group mb-3 col-6">
								<label class="form-label" for="map_location">{{ trans('cruds.provider.fields.map_location') }}</label>
								<input class="form-control form-control-sm {{ $errors->has('map_location') ? 'is-invalid' : '' }}" type="text" name="map_location" id="map_location" value="{{ old('map_location', $provider->map_location) }}">
								@if ($errors->has('map_location'))
									<div class="invalid-feedback">
										{{ $errors->first('map_location') }}
									</div>
								@endif
								<span class="help-block">{{ trans('cruds.provider.fields.map_location_helper') }}</span>
							</div>
							<div class="form-group mb-3 col-12">
								<label class="form-label" for="location">{{ trans('cruds.provider.fields.location') }}</label>
								<textarea class="form-control {{ $errors->has('location') ? 'is-invalid' : '' }}" name="location" id="location">{{ old('location', $provider->location) }}</textarea>
								@if ($errors->has('location'))
									<div class="invalid-feedback">
										{{ $errors->first('location') }}
									</div>
								@endif
								<span class="help-block">{{ trans('cruds.provider.fields.location_helper') }}</span>
							</div> --}}
                            <div class="form-group mb-3 col-12">
                                <label class="form-label" for="description">{{ trans('cruds.provider.fields.description') }}</label>
                                <textarea class="form-control ckeditor {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description">{!! old('description', $provider->description) !!}</textarea>
                                @if ($errors->has('description'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('description') }}
                                    </div>
                                @endif
                                <span class="help-block">{{ trans('cruds.provider.fields.description_helper') }}</span>
                            </div>


                        </div>

                        <div class="form-group mb-3">
                            <button class="btn btn-sm btn-danger" type="submit">
                                {{ trans('global.save') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="kt_drawer" class="bg-white drawer_crud d-none" data-kt-drawer-activate="true" data-kt-drawer-close="#close_drawer" data-kt-drawer-overlay="true">
        <div class="card w-100" id="drawer_card">
        </div>
    </div>


@endsection

@section('scripts')
    <script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.js"></script>
    <script src="{{ url('/') }}/assets/js/drawer.js"></script>
    <script src="{{ asset('assets/js/media-library.js') }}"></script>
    <script>
        // Format options
        var optionFormat = function(item) {
            if (!item.id) {
                return item.text;
            }

            var span = document.createElement('span');
            var imgUrl = item.element.getAttribute('data-kt-select2-country');
            var template = '';

            template += '<img src="' + imgUrl + '" class="rounded-circle h-20px me-2" alt="image"/>';
            template += item.text;

            span.innerHTML = template;

            return $(span);
        }

        // Init Select2 --- more info: https://select2.org/
        $('#kt_docs_select2_country').select2({
            templateSelection: optionFormat,
            templateResult: optionFormat
        });

        // Logo preview functionality
        $(document).ready(function() {
            // Add logo preview when URL changes
            $('#logo').on('change input', function() {
                const logoUrl = $(this).val();
                previewLogo(logoUrl);
            });

            function previewLogo(url) {
                const preview = $('#logo_preview');
                if (url && url.trim() !== '') {
                    preview.attr('src', url);
                    preview.show();
                } else {
                    preview.hide();
                }
            }

            // Trigger preview on page load if there's already a value
            if ($('#logo').val()) {
                previewLogo($('#logo').val());
            }
        });
    </script>

    <script>
        var csrfToken = "{{ csrf_token() }}";
        // Call function for ProviderType
        handleFormActions(
            'provider_type', // form id (any unique)
            'provider_type_id', // select id (from html)
            'drawer_provider_type', // drawer id (any unique)
            'ProviderType', // modelClass: model class name (from what model the data is fetch)
            'providerTypes', // crudFolderName: crud folder name ( same as view/admin )
            "{{ route('admin.provider-types.getForm') }}", // drawerFormUrl: url to get the form
            'providerType', // crudName : use for lang (trans)
            'provider-types', // routeName : route from url
            @json(auth()->user()->can('provider_type_create')), // canCreate : permission
            @json(auth()->user()->can('provider_type_edit')), // canEdit : permission
            'id', // idField : option value
            'name', // displayField : option text
            'providerType' // responseKey : reponse key from controller
        );

        // call function for Market
        handleFormActions(
            'market', // form id (any unique)
            'market_id', // select id (from html)
            'drawer_market', // drawer id (any unique)
            'Market', // modelClass: model class name (from what model the data is fetch)
            'markets', // crudFolderName: crud folder name ( same as view/admin )
            "{{ route('admin.markets.getForm') }}", // drawerFormUrl: url to get the form
            'market', // crudName : use for lang (trans)
            'markets', // routeName : route from url
            @json(auth()->user()->can('market_create')), // canCreate : permission
            @json(auth()->user()->can('market_edit')), // canEdit : permission
            'id', // idField : option value
            'name', // displayField : option text
            'market' // responseKey : reponse key from controller
        );

        // Call function for ProviderCategory
        handleFormActions(
            'provider_catergory', // form id (any unique)
            'provider_catergory_id', // select id (from html)
            'drawer_provider_catergory', // drawer id (any unique)
            'ProviderCatergory', // modelClass: model class name (from what model the data is fetch)
            'providerCatergories', // crudFolderName: crud folder name ( same as view/admin )
            "{{ route('admin.provider-catergories.getForm') }}", // drawerFormUrl: url to get the form
            'providerCatergory', // crudName : use for lang (trans)
            'provider-catergories', // routeName : route from url
            @json(auth()->user()->can('provider_catergory_create')), // canCreate : permission
            @json(auth()->user()->can('provider_catergory_edit')), // canEdit : permission
            'id', // idField : option value
            'name', // displayField : option text
            'providerCatergory' // responseKey : reponse key from controller
        );
    </script>

    <script>
        $(document).ready(function() {
            let termAndConditionEditor;

            ClassicEditor
                .create(document.querySelector('#description'))
                .then(editor => {
                    termAndConditionEditor = editor;
                })
                .catch(error => {
                    console.error(error);
                });
        });
    </script>

    <script>
        Dropzone.options.logoDropzone = {
            url: '{{ route('admin.providers.storeMedia') }}',
            maxFilesize: 20, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            maxFiles: 1,
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 20,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').find('input[name="logo"]').remove()
                $('form').append('<input type="hidden" name="logo" value="' + response.name + '">')
            },
            removedfile: function(file) {
                file.previewElement.remove()
                if (file.status !== 'error') {
                    $('form').find('input[name="logo"]').remove()
                    this.options.maxFiles = this.options.maxFiles + 1
                }
            },
            init: function() {
                @if (isset($provider) && $provider->logo)
                    var file = {!! json_encode($provider->logo) !!}
                    this.options.addedfile.call(this, file)
                    this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                    file.previewElement.classList.add('dz-complete')
                    $('form').append('<input type="hidden" name="logo" value="' + file.file_name + '">')
                    this.options.maxFiles = this.options.maxFiles - 1
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>

    <script>
        var uploadedSliderMap = {}
        Dropzone.options.sliderDropzone = {
            url: '{{ route('admin.providers.storeMedia') }}',
            maxFilesize: 20, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 20,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').append('<input type="hidden" name="slider[]" value="' + response.name + '">')
                uploadedSliderMap[file.name] = response.name
            },
            removedfile: function(file) {
                console.log(file)
                file.previewElement.remove()
                var name = ''
                if (typeof file.file_name !== 'undefined') {
                    name = file.file_name
                } else {
                    name = uploadedSliderMap[file.name]
                }
                $('form').find('input[name="slider[]"][value="' + name + '"]').remove()
            },
            init: function() {
                @if (isset($provider) && $provider->slider)
                    var files = {!! json_encode($provider->slider) !!}
                    for (var i in files) {
                        var file = files[i]
                        this.options.addedfile.call(this, file)
                        this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                        file.previewElement.classList.add('dz-complete')
                        $('form').append('<input type="hidden" name="slider[]" value="' + file.file_name + '">')
                    }
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>
@endsection
