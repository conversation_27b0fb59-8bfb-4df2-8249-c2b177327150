<?php

return [
    'userManagement' => [
        'title'          => 'Utilisateurs',
        'title_singular' => 'Gestion des utilisateurs',
    ],
    'mediaLibrary' => [
        'title'          => 'Bibliothèque Média',
        'title_singular' => 'Bibliothèque Média',
        'fields'         => [
            'id'                 => 'ID',
            'name'               => 'Nom',
            'file_name'          => 'Nom du fichier',
            'mime_type'          => 'Type',
            'size'               => 'Taille',
            'created_at'         => 'Créé le',
            'updated_at'         => 'Mis à jour le',
        ],
    ],
    'market' => [
        'title'          => 'Marché',
        'title_singular' => 'Marché',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'type_city'          => 'Ville',
            'type_state'         => 'État',
            'type_country'       => 'Pays',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'status_active'      => 'Actif',
            'status_inactive'    => 'Inactif',
            'status_draft'       => 'Brouillon',
            'description'        => 'Description',
            'description_helper' => ' ',
            'photo'              => 'Photo',
            'photo_helper'       => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
            'team'               => 'Équipe',
            'team_helper'        => ' ',
            'provider'           => 'Fournisseur',
            'provider_helper'    => ' ',
        ],
    ],
    'permission' => [
        'title'          => 'Permissions',
        'title_singular' => 'Permission',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'title'             => 'Titre',
            'title_helper'      => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],
    'role' => [
        'title'          => 'Rôles',
        'title_singular' => 'Rôle',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'title'              => 'Titre',
            'title_helper'       => ' ',
            'permissions'        => 'Permissions',
            'permissions_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'user' => [
        'title'          => 'Utilisateurs',
        'title_singular' => 'Utilisateur',
        'fields'         => [
            'id'                           => 'ID',
            'id_helper'                    => ' ',
            'name'                         => 'Nom',
            'name_helper'                  => ' ',
            'email'                        => 'Email',
            'email_helper'                 => ' ',
            'email_verified_at'            => 'Email vérifié le',
            'email_verified_at_helper'     => ' ',
            'password'                     => 'Mot de passe',
            'password_helper'              => ' ',
            'roles'                        => 'Rôles',
            'roles_helper'                 => ' ',
            'remember_token'               => 'Jeton de rappel',
            'remember_token_helper'        => ' ',
            'created_at'                   => 'Créé le',
            'created_at_helper'            => ' ',
            'updated_at'                   => 'Mis à jour le',
            'updated_at_helper'            => ' ',
            'deleted_at'                   => 'Supprimé le',
            'deleted_at_helper'            => ' ',
            'team'                         => 'Équipe',
            'team_helper'                  => ' ',
            'two_factor'                   => 'Authentification à deux facteurs',
            'two_factor_helper'            => ' ',
            'two_factor_code'              => 'Code à deux facteurs',
            'two_factor_code_helper'       => ' ',
            'two_factor_expires_at'        => 'Expiration à deux facteurs',
            'two_factor_expires_at_helper' => ' ',
        ],
    ],
    'auditLog' => [
        'title'          => 'Journaux d\'audit',
        'title_singular' => 'Journal d\'audit',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'description'         => 'Description',
            'description_helper'  => ' ',
            'subject_id'          => 'ID du sujet',
            'subject_id_helper'   => ' ',
            'subject_type'        => 'Type de sujet',
            'subject_type_helper' => ' ',
            'user_id'             => 'ID utilisateur',
            'user_id_helper'      => ' ',
            'properties'          => 'Propriétés',
            'properties_helper'   => ' ',
            'host'                => 'Hôte',
            'host_helper'         => ' ',
            'created_at'          => 'Créé le',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Mis à jour le',
            'updated_at_helper'   => ' ',
        ],
    ],
    'team' => [
        'title'          => 'Équipes',
        'title_singular' => 'Équipe',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
            'name'              => 'Nom',
            'name_helper'       => ' ',
            'owner'             => 'Propriétaire',
            'owner_helper'      => ' ',
        ],
    ],
    'providerManagement' => [
        'title'          => 'Fournisseurs',
        'title_singular' => 'Gestion des fournisseurs',
    ],
    'provider' => [
        'title'          => 'Fournisseur',
        'title_singular' => 'Fournisseur',
        'fields'         => [
            'id'                        => 'ID',
            'id_helper'                 => ' ',
            'name'                      => 'Nom',
            'name_helper'               => ' ',
            'description'               => 'Description',
            'description_helper'        => ' ',
            'phone'                     => 'Téléphone',
            'phone_helper'              => ' ',
            'email'                     => 'Email',
            'email_helper'              => ' ',
            'status'                    => 'Statut',
            'status_helper'             => ' ',
            'website_url'               => 'URL du site web',
            'website_url_helper'        => ' ',
            'logo'                      => 'Logo',
            'logo_helper'               => ' ',
            'type'                      => 'Type',
            'type_helper'               => ' ',
            'created_at'                => 'Créé le',
            'created_at_helper'         => ' ',
            'updated_at'                => 'Mis à jour le',
            'updated_at_helper'         => ' ',
            'deleted_at'                => 'Supprimé le',
            'deleted_at_helper'         => ' ',
            'provider_catergory'        => 'Catégorie de fournisseur',
            'provider_catergory_helper' => ' ',
            'slider'                    => 'Slider',
            'slider_helper'             => ' ',
            'vat_number'                => 'Numéro TVA',
            'vat_number_helper'         => ' ',
            'phone_code'                => 'Indicatif téléphonique',
            'phone_code_helper'         => ' ',
            'market'                    => 'Marché',
            'market_helper'             => ' ',
            'facebook'                  => 'Facebook',
            'facebook_helper'           => ' ',
            'instagram'                 => 'Instagram',
            'instagram_helper'          => ' ',
            'youtube'                   => 'Youtube',
            'youtube_helper'            => ' ',
            'linkedin'                  => 'Linkedin',
            'linkedin_helper'           => ' ',
            'tiktok'                    => 'Tiktok',
            'tiktok_helper'             => ' ',
            'provider_type'             => 'Type de fournisseur',
            'provider_type_helper'      => ' ',
            'telephone'                 => 'Téléphone',
            'telephone_helper'          => ' ',
            'brn'                       => 'BRN',
            'brn_helper'                => ' ',
            'location'                  => 'Emplacement',
            'location_helper'           => ' ',
            'map_location'              => 'Emplacement sur la carte',
            'map_location_helper'       => ' ',
        ],
    ],

    'providerCatergory' => [
        'title'          => 'Catégorie de fournisseur',
        'title_singular' => 'Catégorie de fournisseur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'providerType' => [
        'title'          => 'Type de fournisseur',
        'title_singular' => 'Type de fournisseur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'setting' => [
        'title'          => 'Paramètre',
        'title_singular' => 'Paramètre',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'meta_key'          => 'Clé Meta',
            'meta_key_helper'   => ' ',
            'meta_value'        => 'Valeur Meta',
            'meta_value_helper' => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],
    'currency' => [
        'title'          => 'Devise',
        'title_singular' => 'Devise',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Nom',
            'name_helper'       => ' ',
            'symbol'            => 'Symbole',
            'symbol_helper'     => ' ',
            'position'          => 'Position',
            'position_helper'   => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],
    'dayOfWeek' => [
        'title'          => 'Jour de la semaine',
        'title_singular' => 'Jour de la semaine',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'season' => [
        'title'          => 'Saison',
        'title_singular' => 'Saison',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'active'             => 'Actif',
            'inactive'           => 'Inactif',
            'description'        => 'Description',
            'description_helper' => ' ',
            'start_date'         => 'Date de début du contrat',
            'start_date_helper'  => ' ',
            'end_date'           => 'Date de fin du contrat',
            'end_date_helper'    => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'ageGroup' => [
        'title'          => 'Groupe d\'âge',
        'title_singular' => 'Groupe d\'âge',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'min_age'            => 'Âge minimum',
            'min_age_helper'     => ' ',
            'max_age'            => 'Âge maximum',
            'max_age_helper'     => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
            'provider'           => 'Fournisseur',
            'provider_helper'    => ' ',
        ],
    ],
    'packageManagement' => [
        'title'          => 'Forfaits',
        'title_singular' => 'Gestion des forfaits',
    ],
    'package' => [
        'title'          => 'Forfait',
        'title_singular' => 'Forfait',
        'fields'         => [
            'id'                     => 'ID',
            'id_helper'              => ' ',
            'title'                  => 'Titre',
            'title_helper'           => ' ',
            'description'            => 'Description',
            'description_helper'     => ' ',
            'start_date'             => 'Date de début du contrat',
            'start_date_helper'      => ' ',
            'end_date'               => 'Date de fin du contrat',
            'end_date_helper'        => ' ',
            'image'                  => 'Image',
            'image_helper'           => ' ',
            'banner'                 => 'Bannière',
            'banner_helper'          => ' ',
            'slider'                 => 'Slider',
            'slider_helper'          => ' ',
            'provider'               => 'Fournisseur',
            'provider_helper'        => ' ',
            'created_at'             => 'Créé le',
            'created_at_helper'      => ' ',
            'updated_at'             => 'Mis à jour le',
            'updated_at_helper'      => ' ',
            'deleted_at'             => 'Supprimé le',
            'deleted_at_helper'      => ' ',
            'max_participant'        => 'Nombre maximum de participants',
            'max_participant_helper' => ' ',
            'min_participant'        => 'Nombre minimum de participants',
            'min_participant_helper' => ' ',
            'pricing_model'          => 'Plan tarifaire',
            'pricing_model_helper'   => ' ',
            'package_type'           => 'Type de forfait',
            'package_type_helper'    => ' ',
            'rating'                 => 'Évaluation',
            'rating_helper'          => ' ',
            'status'                 => 'Statut',
            'status_helper'          => ' ',
            'pass'                   => 'Pass',
            'pass_helper'            => ' ',
        ],
    ],

    'packageSchedule' => [
        'title'          => 'Planning de forfait',
        'title_singular' => 'Planning de forfait',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'package'             => 'Forfait',
            'package_helper'      => ' ',
            'start_time'          => 'Heure de début',
            'start_time_helper'   => ' ',
            'end_time'            => 'Heure de fin',
            'end_time_helper'     => ' ',
            'is_recurring'        => 'Est récurrent',
            'is_recurring_helper' => ' ',
            'start_date'          => 'Date de début du contrat',
            'start_date_helper'   => ' ',
            'end_date'            => 'Date de fin du contrat',
            'end_date_helper'     => ' ',
            'day_of_week'         => 'Jour de la semaine',
            'day_of_week_helper'  => ' ',
            'created_at'          => 'Créé le',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Mis à jour le',
            'updated_at_helper'   => ' ',
            'deleted_at'          => 'Supprimé le',
            'deleted_at_helper'   => ' ',
        ],
    ],

    'packagePrice' => [
        'title'          => 'Prix du forfait',
        'title_singular' => 'Prix du forfait',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'package'                    => 'Forfait',
            'package_helper'             => ' ',
            'season'                     => 'Saison',
            'season_helper'              => ' ',
            'currency'                   => 'Devise',
            'currency_helper'            => ' ',
            'age_group'                  => 'Groupe d\'âge',
            'age_group_helper'           => ' ',
            'price'                      => 'Prix de vente',
            'price_helper'               => ' ',
            'cost_price'                 => 'Prix de revient',
            'cost_price_helper'          => ' ',
            'issuer_price'               => 'Prix émetteur',
            'issuer_price_helper'        => ' ',
            'issuer'                     => 'Émetteur',
            'issuer_helper'              => ' ',
            'is_percentage'              => 'En pourcentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Pourcentage de remise',
            'discount_percentage_helper' => ' ',
            'created_at'                 => 'Créé le',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Mis à jour le',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Supprimé le',
            'deleted_at_helper'          => ' ',
            'customer_group'             => 'Groupe de clients',
            'customer_group_helper'      => ' ',
            'provider_age_group'         => 'Groupe d\'âge du fournisseur',
            'provider_age_group_helper'  => ' ',
            'start_date'                 => 'Date de début du contrat',
            'start_date_helper'          => ' ',
            'end_date'                   => 'Date de fin du contrat',
            'end_date_helper'            => ' ',
        ],
    ],
    'country' => [
        'title'          => 'Pays',
        'title_singular' => 'Pays',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Nom',
            'name_helper'       => ' ',
            'short_code'        => 'Code court',
            'short_code_helper' => ' ',
            'code'              => 'Code',
            'code_helper'       => ' ',
            'phone_code'        => 'Indicatif téléphonique',
            'phone_code_helper' => ' ',
            'flag'              => 'Drapeau',
            'flag_helper'       => ' ',
            'status'            => 'Statut',
            'status_helper'     => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],
    'packageLocation' => [
        'title'          => 'Lieu du forfait',
        'title_singular' => 'Lieu du forfait',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'address_1'          => 'Adresse 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Adresse 2',
            'address_2_helper'   => ' ',
            'state'              => 'État',
            'state_helper'       => ' ',
            'city'               => 'Ville',
            'city_helper'        => ' ',
            'postal_code'        => 'Code postal',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'package'            => 'Forfait',
            'package_helper'     => ' ',
            'country'            => 'Pays',
            'country_helper'     => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'providerLocation' => [
        'title'          => 'Lieu du fournisseur',
        'title_singular' => 'Lieu du fournisseur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Adresse 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Adresse 2',
            'address_2_helper'   => ' ',
            'state'              => 'État',
            'state_helper'       => ' ',
            'city'               => 'Ville',
            'city_helper'        => ' ',
            'postal_code'        => 'Code postal',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'provider'           => 'Fournisseur',
            'provider_helper'    => ' ',
            'country'            => 'Pays',
            'country_helper'     => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
            'email'              => 'Email',
            'email_helper'       => ' ',
            'contact_number'     => 'Numéro de contact',
            'contact_number_helper' => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'country_code_id'    => 'Indicatif pays',
            'country_code_id_helper' => ' ',
            'map_location'       => 'Emplacement sur la carte',
            'map_location_helper' => ' ',
            'telephone'          => 'Téléphone',
            'telephone_helper'   => ' ',
            'phone'              => 'Téléphone',
            'phone_helper'       => ' ',
        ],
    ],


    'customerManagement' => [
        'title'          => 'Clients',
        'title_singular' => 'Gestion des clients',
    ],
    'customer' => [
        'title'          => 'Client',
        'title_singular' => 'Client',
        'fields'         => [
            'id'                        => 'ID',
            'id_helper'                 => ' ',
            'first_name'                => 'Prénom',
            'first_name_helper'         => ' ',
            'last_name'                 => 'Nom de famille',
            'last_name_helper'          => ' ',
            'date_of_birth'             => 'Date de naissance',
            'date_of_birth_helper'      => ' ',
            'mobile'                    => 'Mobile',
            'mobile_helper'             => ' ',
            'mobile_verified_at'        => 'Mobile vérifié le',
            'mobile_verified_at_helper' => ' ',
            'telephone'                 => 'Téléphone',
            'telephone_helper'          => ' ',
            'profile_image'             => 'Image de profil',
            'profile_image_helper'      => ' ',
            'password'                  => 'Mot de passe',
            'password_helper'           => ' ',
            'pin'                       => 'Code PIN',
            'pin_helper'                => ' ',
            'created_at'                => 'Créé le',
            'created_at_helper'         => ' ',
            'updated_at'                => 'Mis à jour le',
            'updated_at_helper'         => ' ',
            'deleted_at'                => 'Supprimé le',
            'deleted_at_helper'         => ' ',
            'gender'                    => 'Genre',
            'gender_helper'             => ' ',
            'otp'                       => 'Code OTP',
            'otp_helper'                => ' ',
            'otp_expired_at'            => 'OTP expiré le',
            'otp_expired_at_helper'     => ' ',
            'customer_group'            => 'Groupe de clients',
            'customer_group_helper'     => ' ',
        ],
    ],

    'customerGroup' => [
        'title'          => 'Groupe de clients',
        'title_singular' => 'Groupe de clients',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'gender' => [
        'title'          => 'Genre',
        'title_singular' => 'Genre',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'customerAddress' => [
        'title'          => 'Adresse du client',
        'title_singular' => 'Adresse du client',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Adresse 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Adresse 2',
            'address_2_helper'   => ' ',
            'state'              => 'État',
            'state_helper'       => ' ',
            'city'               => 'Ville',
            'city_helper'        => ' ',
            'postal_code'        => 'Code postal',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'country'            => 'Pays',
            'country_helper'     => ' ',
            'customer'           => 'Client',
            'customer_helper'    => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'customerEmail' => [
        'title'          => 'Email du client',
        'title_singular' => 'Email du client',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'type'                     => 'Type',
            'type_helper'              => ' ',
            'email'                    => 'Email',
            'email_helper'             => ' ',
            'email_verified_at'        => 'Email vérifié le',
            'email_verified_at_helper' => ' ',
            'created_at'               => 'Créé le',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Mis à jour le',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Supprimé le',
            'deleted_at_helper'        => ' ',
            'customer'                 => 'Client',
            'customer_helper'          => ' ',
        ],
    ],

    'customerDetail' => [
        'title'          => 'Détails du client',
        'title_singular' => 'Détail du client',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'customer'          => 'Client',
            'customer_helper'   => ' ',
            'passport'          => 'Passeport',
            'passport_helper'   => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],


    'issuerManagement' => [
        'title'          => 'Émetteurs',
        'title_singular' => 'Gestion des émetteurs',
    ],

    'issuerCatergory' => [
        'title'          => 'Catégorie d\'émetteur',
        'title_singular' => 'Catégorie d\'émetteur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'issuerLocation' => [
        'title'          => 'Localisation d\'émetteur',
        'title_singular' => 'Localisation d\'émetteur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Adresse 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Adresse 2',
            'address_2_helper'   => ' ',
            'state'              => 'État',
            'state_helper'       => ' ',
            'city'               => 'Ville',
            'city_helper'        => ' ',
            'postal_code'        => 'Code postal',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'country'            => 'Pays',
            'country_helper'     => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
            'issuer'             => 'Émetteur',
            'issuer_helper'      => ' ',
            'map_location'       => 'Localisation sur la carte',
            'map_location_helper' => ' ',
            'telephone'          => 'Téléphone',
            'telephone_helper'   => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'phone'              => 'Téléphone',
            'phone_helper'       => ' ',
            'email'              => 'Email',
            'email_helper'       => ' ',
        ],
    ],

    'issuer' => [
        'title'          => 'Émetteur',
        'title_singular' => 'Émetteur',
        'fields'         => [
            'id'                      => 'ID',
            'id_helper'               => ' ',
            'name'                    => 'Nom',
            'name_helper'             => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'phone_code'              => 'Indicatif téléphonique',
            'phone_code_helper'       => ' ',
            'phone'                   => 'Téléphone',
            'phone_helper'            => ' ',
            'email'                   => 'Email',
            'email_helper'            => ' ',
            'website_url'             => 'URL du site web',
            'website_url_helper'      => ' ',
            'vat_number'              => 'Numéro de TVA',
            'vat_number_helper'       => ' ',
            'brn'                     => 'BRN',
            'brn_helper'              => ' ',
            'status'                  => 'Statut',
            'status_helper'           => ' ',
            'logo'                    => 'Logo',
            'logo_helper'             => ' ',
            'issuer_catergory'        => 'Catégorie de l’émetteur',
            'issuer_catergory_helper' => ' ',
            'created_at'              => 'Créé le',
            'created_at_helper'       => ' ',
            'slider'                  => 'Slider',
            'slider_helper'           => ' ',
            'updated_at'              => 'Mis à jour le',
            'updated_at_helper'       => ' ',
            'deleted_at'              => 'Supprimé le',
            'deleted_at_helper'       => ' ',
            'map_location'            => 'Localisation sur la carte',
            'map_location_helper'     => ' ',
            'telephone'               => 'Téléphone',
            'telephone_helper'        => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'phone'                   => 'Téléphone',
            'phone_helper'            => ' ',
            'email'                   => 'Email',
            'email_helper'            => ' ',
        ],
    ],
    'guideManagement' => [
        'title'          => 'Guides',
        'title_singular' => 'Gestion des guides',
    ],
    'guideCategory' => [
        'title'          => 'Catégorie de guide',
        'title_singular' => 'Catégorie de guide',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'guide' => [
        'title'          => 'Guide',
        'title_singular' => 'Guide',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'is_question_guide'        => 'Est un guide à questions',
            'is_question_guide_helper' => ' ',
            'title'                    => 'Titre',
            'title_helper'             => ' ',
            'content'                  => 'Contenu',
            'content_helper'           => ' ',
            'logo'                     => 'Logo',
            'logo_helper'              => ' ',
            'background_image'         => 'Image de fond',
            'background_image_helper'  => ' ',
            'url'                      => 'URL',
            'url_helper'               => ' ',
            'created_at'               => 'Créé le',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Mis à jour le',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Supprimé le',
            'deleted_at_helper'        => ' ',
            'position'                 => 'Position',
            'position_helper'          => ' ',
        ],
    ],
    'guideQuestion' => [
        'title'          => 'Question de guide',
        'title_singular' => 'Question de guide',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'guide'             => 'Guide',
            'guide_helper'      => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
            'title'             => 'Titre',
            'title_helper'      => ' ',
            'image_1'           => 'Image 1',
            'image_1_helper'    => ' ',
            'image_2'           => 'Image 2',
            'image_2_helper'    => ' ',
            'position'          => 'Position',
            'position_helper'   => ' ',
        ],
    ],
    'guideQuestionAnswer' => [
        'title'          => 'Réponse à la question du guide',
        'title_singular' => 'Réponse à la question du guide',
        'fields'         => [
            'id'                    => 'ID',
            'id_helper'             => ' ',
            'guide_question'        => 'Question du guide',
            'guide_question_helper' => ' ',
            'customer'              => 'Client',
            'customer_helper'       => ' ',
            'answer'                => 'Réponse',
            'answer_helper'         => ' ',
            'answered_at'           => 'Répondu le',
            'answered_at_helper'    => ' ',
            'created_at'            => 'Créé le',
            'created_at_helper'     => ' ',
            'updated_at'            => 'Mis à jour le',
            'updated_at_helper'     => ' ',
            'deleted_at'            => 'Supprimé le',
            'deleted_at_helper'     => ' ',
        ],
    ],
    'guideCustomerProgress' => [
        'title'          => 'Progression du client dans le guide',
        'title_singular' => 'Progression du client dans le guide',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'customer'           => 'Client',
            'customer_helper'    => ' ',
            'answer'             => 'Réponse',
            'answer_helper'      => ' ',
            'answered_at'        => 'Répondu le',
            'answered_at_helper' => ' ',
            'guide'              => 'Guide',
            'guide_helper'       => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'paymentMethod' => [
        'title'          => 'Méthode de paiement',
        'title_singular' => 'Méthode de paiement',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'vatPercentage' => [
        'title'          => 'Pourcentage de TVA',
        'title_singular' => 'Pourcentage de TVA',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Nom',
            'name_helper'       => ' ',
            'value'             => 'Valeur',
            'value_helper'      => ' ',
            'status'            => 'Statut',
            'status_helper'     => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],

    'orderManagement' => [
        'title'          => 'Commandes',
        'title_singular' => 'Gestion des commandes',
    ],

    'order' => [
        'title'          => 'Commande',
        'title_singular' => 'Commande',
        'fields'         => [
            'id'                    => 'ID',
            'id_helper'             => ' ',
            'customer'              => 'Client',
            'customer_helper'       => ' ',
            'issuer'                => 'Émetteur',
            'issuer_helper'         => ' ',
            'package'               => 'Forfait',
            'package_helper'        => ' ',
            'payment_method'        => 'Méthode de paiement',
            'payment_method_helper' => ' ',
            'vat_percentage'        => 'Pourcentage de TVA',
            'vat_percentage_helper' => ' ',
            'status'                => 'Statut',
            'status_helper'         => ' ',
            'order_type'            => 'Type de commande',
            'order_type_helper'     => ' ',
            'total_vat'             => 'TVA totale',
            'total_vat_helper'      => ' ',
            'sub_total'             => 'Sous-total',
            'sub_total_helper'      => ' ',
            'total'                 => 'Total',
            'total_helper'          => ' ',
            'created_at'            => 'Créé le',
            'created_at_helper'     => ' ',
            'updated_at'            => 'Mis à jour le',
            'updated_at_helper'     => ' ',
            'deleted_at'            => 'Supprimé le',
            'deleted_at_helper'     => ' ',
        ],
    ],

    'orderItem' => [
        'title'          => 'Article de commande',
        'title_singular' => 'Article de commande',
        'fields'         => [
            'id'                   => 'ID',
            'id_helper'            => ' ',
            'order'                => 'Commande',
            'order_helper'         => ' ',
            'package_price'        => 'Prix du forfait',
            'package_price_helper' => ' ',
            'package'              => 'Forfait',
            'package_helper'       => ' ',
            'quantity'             => 'Quantité',
            'quantity_helper'      => ' ',
            'price'                => 'Prix',
            'price_helper'         => ' ',
            'vat'                  => 'TVA',
            'vat_helper'           => ' ',
            'total_price'          => 'Prix total',
            'total_price_helper'   => ' ',
            'created_at'           => 'Créé le',
            'created_at_helper'    => ' ',
            'updated_at'           => 'Mis à jour le',
            'updated_at_helper'    => ' ',
            'deleted_at'           => 'Supprimé le',
            'deleted_at_helper'    => ' ',
        ],
    ],

    'passManagement' => [
        'title'          => 'Gestion des pass',
        'title_singular' => 'Gestion des pass',
    ],

    'passType' => [
        'title'          => 'Type de pass',
        'title_singular' => 'Type de pass',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'passStatus' => [
        'title'          => 'Statut du pass',
        'title_singular' => 'Statut du pass',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'pass' => [
        'title'          => 'Pass',
        'title_singular' => 'Pass',
        'fields'         => [
            'id'                     => 'ID',
            'id_helper'              => ' ',
            'title'                  => 'Titre',
            'title_helper'           => ' ',
            'description'            => 'Description',
            'description_helper'     => ' ',
            'logo'                   => 'Logo',
            'logo_helper'            => ' ',
            'backgroud_image'        => 'Image de fond',
            'backgroud_image_helper' => ' ',
            'start_date'             => 'Date de début du contrat',
            'start_date_helper'      => ' ',
            'end_date'               => 'Date de fin du contrat',
            'end_date_helper'        => ' ',
            'register_period_start_date' => 'Début de la période d’enregistrement',
            'register_period_start_date_helper' => ' ',
            'register_period_end_date'   => 'Fin de la période d’enregistrement',
            'register_period_end_date_helper' => ' ',
            'pass_status'           => 'Statut du pass',
            'pass_status_helper'    => ' ',
            'pass_type'             => 'Type de pass',
            'pass_type_helper'      => ' ',
            'issuer'                => 'Émetteur',
            'issuer_helper'         => ' ',
            'created_at'            => 'Créé le',
            'created_at_helper'     => ' ',
            'updated_at'            => 'Mis à jour le',
            'updated_at_helper'     => ' ',
            'deleted_at'            => 'Supprimé le',
            'deleted_at_helper'     => ' ',
        ],
    ],

    'customerPass' => [
        'title'          => 'Pass client',
        'title_singular' => 'Pass client',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'customer'          => 'Client',
            'customer_helper'   => ' ',
            'pass'              => 'Pass',
            'pass_helper'       => ' ',
            'pass_code'         => 'Code du pass',
            'pass_code_helper'  => ' ',
            'price'             => 'Prix',
            'price_helper'      => ' ',
            'start_date'        => 'Date de début du contrat',
            'start_date_helper' => ' ',
            'end_date'          => 'Date de fin du contrat',
            'end_date_helper'   => ' ',
            'status'            => 'Statut',
            'status_helper'     => ' ',
            'created_at'        => 'Créé le',
            'created_at_helper' => ' ',
            'updated_at'        => 'Mis à jour le',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Supprimé le',
            'deleted_at_helper' => ' ',
        ],
    ],

    'voucherManagement' => [
        'title'          => 'Bons de réduction',
        'title_singular' => 'Gestion des bons',
    ],

    'voucherStatus' => [
        'title'          => 'Statut du bon',
        'title_singular' => 'Statut du bon',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'voucherType' => [
        'title'          => 'Voucher Type',
        'title_singular' => 'Voucher Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'voucher' => [
        'title'          => 'Voucher',
        'title_singular' => 'Voucher',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'voucher_code'               => 'Voucher Code',
            'voucher_code_helper'        => ' ',
            'start_date'                 => 'Contract Start Date',
            'start_date_helper'          => ' ',
            'end_date'                   => 'Contract End Date',
            'end_date_helper'            => ' ',
            'redeemed_at'                => 'Redeemed At',
            'redeemed_at_helper'         => ' ',
            'is_percentage'              => 'Is Percentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Discount Percentage',
            'discount_percentage_helper' => ' ',
            'price'                      => 'Price',
            'price_helper'               => ' ',
            'voucher_status'             => 'Voucher Status',
            'voucher_status_helper'      => ' ',
            'voucher_type'               => 'Voucher Type',
            'voucher_type_helper'        => ' ',
            'customer_pass'              => 'Customer Pass',
            'customer_pass_helper'       => ' ',
            'customer'                   => 'Customer',
            'customer_helper'            => ' ',
            'package'                    => 'Package',
            'package_helper'             => ' ',
            'age_group'                  => 'Age Group',
            'age_group_helper'           => ' ',
            'created_at'                 => 'Created at',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Updated at',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Deleted at',
            'deleted_at_helper'          => ' ',
        ],
    ],

    'passPricing' => [
        'title'          => 'Tarification du pass',
        'title_singular' => 'Tarification du pass',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'pass'                       => 'Pass',
            'pass_helper'                => ' ',
            'season'                     => 'Saison',
            'season_helper'              => ' ',
            'currency'                   => 'Devise',
            'currency_helper'            => ' ',
            'age_group'                  => 'Groupe d\'âge',
            'age_group_helper'           => ' ',
            'price'                      => 'Prix',
            'price_helper'               => ' ',
            'is_percentage'              => 'Est un pourcentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Pourcentage de réduction',
            'discount_percentage_helper' => ' ',
            'created_at'                 => 'Créé le',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Mis à jour le',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Supprimé le',
            'deleted_at_helper'          => ' ',
            'customer_group'             => 'Groupe de clients',
            'customer_group_helper'      => ' ',
        ],
    ],

    'packageType' => [
        'title'          => 'Types de forfaits',
        'title_singular' => 'Type de forfait',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'passAddOn' => [
        'title'          => 'Supplément de pass',
        'title_singular' => 'Supplément de pass',
        'fields'         => [
            'id'                      => 'ID',
            'id_helper'               => ' ',
            'title'                   => 'Titre',
            'title_helper'            => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'logo'                    => 'Logo',
            'logo_helper'             => ' ',
            'backgroud_image'         => 'Image de fond',
            'backgroud_image_helper'  => ' ',
            'start_date'              => 'Date de début du contrat',
            'start_date_helper'       => ' ',
            'end_date'                => 'Date de fin du contrat',
            'end_date_helper'         => ' ',
            'pass'                    => 'Pass',
            'pass_helper'             => ' ',
            'created_at'              => 'Créé le',
            'created_at_helper'       => ' ',
            'updated_at'              => 'Mis à jour le',
            'updated_at_helper'       => ' ',
            'deleted_at'              => 'Supprimé le',
            'deleted_at_helper'       => ' ',
            'pass_add_on_type'        => 'Type de supplément',
            'pass_add_on_type_helper' => ' ',
        ],
    ],

    'passAddOnType' => [
        'title'          => 'Type de supplément',
        'title_singular' => 'Type de supplément',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'providerAgeGroup' => [
        'title'          => 'Groupe d\'âge du fournisseur',
        'title_singular' => 'Groupe d\'âge du fournisseur',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'min_age'            => 'Âge minimum',
            'min_age_helper'     => ' ',
            'max_age'            => 'Âge maximum',
            'max_age_helper'     => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'provider'           => 'Fournisseur',
            'provider_helper'    => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'packageLocation' => [
        'title'          => 'Lieu du forfait',
        'title_singular' => 'Lieu du forfait',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Adresse 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Adresse 2',
            'address_2_helper'   => ' ',
            'state'              => 'État',
            'state_helper'       => ' ',
            'city'               => 'Ville',
            'city_helper'        => ' ',
            'postal_code'        => 'Code postal',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'package'            => 'Forfait',
            'package_helper'     => ' ',
            'country'            => 'Pays',
            'country_helper'     => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
            'email'              => 'Email',
            'email_helper'       => ' ',
            'contact_number'     => 'Numéro de contact',
            'contact_number_helper' => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'country_code_id'    => 'Code du pays',
            'country_code_id_helper' => ' ',
            'map_location'       => 'Emplacement sur la carte',
            'map_location_helper' => ' ',
            'telephone'          => 'Téléphone',
            'telephone_helper'   => ' ',
            'phone'              => 'Téléphone',
            'phone_helper'       => ' ',
        ],
    ],

    'bank' => [
        'title'          => 'Banque',
        'title_singular' => 'Banque',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Nom',
            'name_helper'        => ' ',
            'status'             => 'Statut',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Créé le',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Mis à jour le',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Supprimé le',
            'deleted_at_helper'  => ' ',
        ],
    ],

    'providerBank' => [
        'title'          => 'Banque du fournisseur',
        'title_singular' => 'Banque du fournisseur',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'provider'            => 'Fournisseur',
            'provider_helper'     => ' ',
            'bank'                => 'Banque',
            'bank_helper'         => ' ',
            'account_number'      => 'Numéro de compte',
            'account_number_helper' => ' ',
            'account_name'        => 'Nom du compte',
            'account_name_helper' => ' ',
            'branch'              => 'Agence',
            'branch_helper'       => ' ',
            'status'              => 'Statut',
            'status_helper'       => ' ',
            'created_at'          => 'Créé le',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Mis à jour le',
            'updated_at_helper'   => ' ',
            'deleted_at'          => 'Supprimé le',
            'deleted_at_helper'   => ' ',
        ],
    ],
];
