<?php

return [
    'userManagement' => [
        'title'          => 'Users',
        'title_singular' => 'User management',
    ],
    'mediaLibrary' => [
        'title'          => 'Medienbibliothek',
        'title_singular' => 'Medienbibliothek',
        'fields'         => [
            'id'                 => 'ID',
            'name'               => 'Name',
            'file_name'          => 'Dateiname',
            'mime_type'          => 'Typ',
            'size'               => 'Größe',
            'created_at'         => 'Erstellt am',
            'updated_at'         => 'Aktualisiert am',
        ],
    ],
    'market' => [
        'title'          => 'Market',
        'title_singular' => 'Market',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'photo'              => 'Photo',
            'photo_helper'       => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
            'team'               => 'Team',
            'team_helper'        => ' ',
            'provider'           => 'Provider',
            'provider_helper'    => ' ',
        ],
    ],
    'permission' => [
        'title'          => 'Permissions',
        'title_singular' => 'Permission',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'title'             => 'Title',
            'title_helper'      => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'role' => [
        'title'          => 'Roles',
        'title_singular' => 'Role',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'title'              => 'Title',
            'title_helper'       => ' ',
            'permissions'        => 'Permissions',
            'permissions_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'user' => [
        'title'          => 'Users',
        'title_singular' => 'User',
        'fields'         => [
            'id'                           => 'ID',
            'id_helper'                    => ' ',
            'name'                         => 'Name',
            'name_helper'                  => ' ',
            'email'                        => 'Email',
            'email_helper'                 => ' ',
            'email_verified_at'            => 'Email verified at',
            'email_verified_at_helper'     => ' ',
            'password'                     => 'Password',
            'password_helper'              => ' ',
            'roles'                        => 'Roles',
            'roles_helper'                 => ' ',
            'remember_token'               => 'Remember Token',
            'remember_token_helper'        => ' ',
            'created_at'                   => 'Created at',
            'created_at_helper'            => ' ',
            'updated_at'                   => 'Updated at',
            'updated_at_helper'            => ' ',
            'deleted_at'                   => 'Deleted at',
            'deleted_at_helper'            => ' ',
            'team'                         => 'Team',
            'team_helper'                  => ' ',
            'two_factor'                   => 'Two-Factor Auth',
            'two_factor_helper'            => ' ',
            'two_factor_code'              => 'Two-factor code',
            'two_factor_code_helper'       => ' ',
            'two_factor_expires_at'        => 'Two-factor expires at',
            'two_factor_expires_at_helper' => ' ',
        ],
    ],
    'auditLog' => [
        'title'          => 'Audit Logs',
        'title_singular' => 'Audit Log',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'description'         => 'Description',
            'description_helper'  => ' ',
            'subject_id'          => 'Subject ID',
            'subject_id_helper'   => ' ',
            'subject_type'        => 'Subject Type',
            'subject_type_helper' => ' ',
            'user_id'             => 'User ID',
            'user_id_helper'      => ' ',
            'properties'          => 'Properties',
            'properties_helper'   => ' ',
            'host'                => 'Host',
            'host_helper'         => ' ',
            'created_at'          => 'Created at',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Updated at',
            'updated_at_helper'   => ' ',
        ],
    ],
    'team' => [
        'title'          => 'Teams',
        'title_singular' => 'Team',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated At',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted At',
            'deleted_at_helper' => ' ',
            'name'              => 'Name',
            'name_helper'       => ' ',
            'owner'             => 'Owner',
            'owner_helper'      => ' ',
        ],
    ],
    'providerManagement' => [
        'title'          => 'Providers',
        'title_singular' => 'Provider Management',
    ],
    'provider' => [
        'title'          => 'Provider',
        'title_singular' => 'Provider',
        'fields'         => [
            'id'                        => 'ID',
            'id_helper'                 => ' ',
            'name'                      => 'Name',
            'name_helper'               => ' ',
            'description'               => 'Description',
            'description_helper'        => ' ',
            'phone'                     => 'Phone',
            'phone_helper'              => ' ',
            'email'                     => 'Email',
            'email_helper'              => ' ',
            'status'                    => 'Status',
            'status_helper'             => ' ',
            'website_url'               => 'Website Url',
            'website_url_helper'        => ' ',
            'logo'                      => 'Logo',
            'logo_helper'               => ' ',
            'type'                      => 'Type',
            'type_helper'               => ' ',
            'created_at'                => 'Created at',
            'created_at_helper'         => ' ',
            'updated_at'                => 'Updated at',
            'updated_at_helper'         => ' ',
            'deleted_at'                => 'Deleted at',
            'deleted_at_helper'         => ' ',
            'provider_catergory'        => 'Provider Category',
            'provider_catergory_helper' => ' ',
            'slider'                    => 'Slider',
            'slider_helper'             => ' ',
            'vat_number'                => 'Vat Number',
            'vat_number_helper'         => ' ',
            'phone_code'                => 'Country Code',
            'phone_code_helper'         => ' ',
            'market'                    => 'Market',
            'market_helper'             => ' ',
            'facebook'                  => 'Facebook',
            'facebook_helper'           => ' ',
            'instagram'                 => 'Instagram',
            'instagram_helper'          => ' ',
            'youtube'                   => 'Youtube',
            'youtube_helper'            => ' ',
            'linkedin'                  => 'Linkedin',
            'linkedin_helper'           => ' ',
            'tiktok'                    => 'Tiktok',
            'tiktok_helper'             => ' ',
            'provider_type'             => 'Provider Type',
            'provider_type_helper'      => ' ',
            'telephone'                 => 'Telephone',
            'telephone_helper'          => ' ',
            'brn'                       => 'BRN',
            'brn_helper'                => ' ',
            'location'                  => 'Location',
            'location_helper'           => ' ',
            'map_location'              => 'Map Location',
            'map_location_helper'       => ' ',
        ],
    ],
    'providerCatergory' => [
        'title'          => 'Provider Category',
        'title_singular' => 'Provider Category',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'setting' => [
        'title'          => 'Setting',
        'title_singular' => 'Setting',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'meta_key'          => 'Meta Key',
            'meta_key_helper'   => ' ',
            'meta_value'        => 'Meta Value',
            'meta_value_helper' => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'currency' => [
        'title'          => 'Currency',
        'title_singular' => 'Currency',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Name',
            'name_helper'       => ' ',
            'symbol'            => 'Symbol',
            'symbol_helper'     => ' ',
            'position'          => 'Position',
            'position_helper'   => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'dayOfWeek' => [
        'title'          => 'Day Of Week',
        'title_singular' => 'Day Of Week',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'season' => [
        'title'          => 'Season',
        'title_singular' => 'Season',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'start_date'         => 'Contract Start Date',
            'start_date_helper'  => ' ',
            'end_date'           => 'Contract End Date',
            'end_date_helper'    => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'ageGroup' => [
        'title'          => 'Age Group',
        'title_singular' => 'Age Group',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'min_age'            => 'Min Age',
            'min_age_helper'     => ' ',
            'max_age'            => 'Max Age',
            'max_age_helper'     => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
            'provider'           => 'Provider',
            'provider_helper'    => ' ',
        ],
    ],
    'packageManagement' => [
        'title'          => 'Packages',
        'title_singular' => 'Package Management',
    ],
    'package' => [
        'title'          => 'Package',
        'title_singular' => 'Package',
        'fields'         => [
            'id'                     => 'ID',
            'id_helper'              => ' ',
            'title'                  => 'Title',
            'title_helper'           => ' ',
            'description'            => 'Description',
            'description_helper'     => ' ',
            'start_date'             => 'Contract Start Date',
            'start_date_helper'      => ' ',
            'end_date'               => 'Contract End Date',
            'end_date_helper'        => ' ',
            'image'                  => 'Image',
            'image_helper'           => ' ',
            'banner'                 => 'Banner',
            'banner_helper'          => ' ',
            'slider'                 => 'Slider',
            'slider_helper'          => ' ',
            'provider'               => 'Provider',
            'provider_helper'        => ' ',
            'created_at'             => 'Created at',
            'created_at_helper'      => ' ',
            'updated_at'             => 'Updated at',
            'updated_at_helper'      => ' ',
            'deleted_at'             => 'Deleted at',
            'deleted_at_helper'      => ' ',
            'max_participant'        => 'Max Participant',
            'max_participant_helper' => ' ',
            'min_participant'        => 'Min Participant',
            'min_participant_helper' => ' ',
            'pricing_model'          => 'Rate Plan',
            'pricing_model_helper'   => ' ',
            'package_type'           => 'Package Type',
            'package_type_helper'    => ' ',
            'rating'                 => 'Rating',
            'rating_helper'          => ' ',
            'status'                 => 'Status',
            'status_helper'          => ' ',
            'pass'                   => 'Pass',
            'pass_helper'            => ' ',
        ],
    ],
    'packageSchedule' => [
        'title'          => 'Package Schedule',
        'title_singular' => 'Package Schedule',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'package'             => 'Package',
            'package_helper'      => ' ',
            'start_time'          => 'Start Time',
            'start_time_helper'   => ' ',
            'end_time'            => 'End Time',
            'end_time_helper'     => ' ',
            'is_recurring'        => 'Is Recurring',
            'is_recurring_helper' => ' ',
            'start_date'          => 'Contract Start Date',
            'start_date_helper'   => ' ',
            'end_date'            => 'Contract End Date',
            'end_date_helper'     => ' ',
            'day_of_week'         => 'Day Of Week',
            'day_of_week_helper'  => ' ',
            'created_at'          => 'Created at',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Updated at',
            'updated_at_helper'   => ' ',
            'deleted_at'          => 'Deleted at',
            'deleted_at_helper'   => ' ',
        ],
    ],
    'packagePrice' => [
        'title'          => 'Package Price',
        'title_singular' => 'Package Price',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'package'                    => 'Package',
            'package_helper'             => ' ',
            'season'                     => 'Season',
            'season_helper'              => ' ',
            'currency'                   => 'Currency',
            'currency_helper'            => ' ',
            'age_group'                  => 'Age Group',
            'age_group_helper'           => ' ',
            'price'                      => 'Selling Price',
            'price_helper'               => ' ',
            'cost_price'                 => 'Cost Price',
            'cost_price_helper'          => ' ',
            'issuer_price'               => 'Issuer Price',
            'issuer_price_helper'        => ' ',
            'issuer'                     => 'Issuer',
            'issuer_helper'              => ' ',
            'is_percentage'              => 'Is Percentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Discount Percentage',
            'discount_percentage_helper' => ' ',
            'created_at'                 => 'Created at',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Updated at',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Deleted at',
            'deleted_at_helper'          => ' ',
            'customer_group'             => 'Customer Group',
            'customer_group_helper'      => ' ',
            'provider_age_group'         => 'Provider Age Group',
            'provider_age_group_helper'  => ' ',
            'start_date'                 => 'Contract Start Date',
            'start_date_helper'          => ' ',
            'end_date'                   => 'Contract End Date',
            'end_date_helper'            => ' ',
        ],
    ],
    'country' => [
        'title'          => 'Country',
        'title_singular' => 'Country',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Name',
            'name_helper'       => ' ',
            'short_code'        => 'Short Code',
            'short_code_helper' => ' ',
            'code'              => 'Code',
            'code_helper'       => ' ',
            'phone_code'        => 'Phone Code',
            'phone_code_helper' => ' ',
            'flag'              => 'Flag',
            'flag_helper'       => ' ',
            'status'            => 'Status',
            'status_helper'     => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'packageLocation' => [
        'title'          => 'Package Location',
        'title_singular' => 'Package Location',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'address_1'          => 'Address 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Address 2',
            'address_2_helper'   => ' ',
            'state'              => 'State',
            'state_helper'       => ' ',
            'city'               => 'City',
            'city_helper'        => ' ',
            'postal_code'        => 'Postal Code',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'package'            => 'Package',
            'package_helper'     => ' ',
            'country'            => 'Country',
            'country_helper'     => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'providerLocation' => [
        'title'          => 'Provider Location',
        'title_singular' => 'Provider Location',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Address 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Address 2',
            'address_2_helper'   => ' ',
            'state'              => 'State',
            'state_helper'       => ' ',
            'city'               => 'City',
            'city_helper'        => ' ',
            'postal_code'        => 'Postal Code',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'provider'           => 'Provider',
            'provider_helper'    => ' ',
            'country'            => 'Country',
            'country_helper'     => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
            'email'                 => 'Email',
            'email_helper'          => ' ',
            'contact_number'        => 'Contact Number',
            'contact_number_helper' => ' ',
            'description'           => 'Description',
            'description_helper'    => ' ',
            'country_code_id'        => 'Country Code',
            'country_code_id_helper' => ' ',
            'map_location'            => 'Map Location',
            'map_location_helper'     => ' ',
            'telephone'               => 'Telephone',
            'telephone_helper'        => ' ',
            'phone'                   => 'Phone',
            'phone_helper'            => ' ',
        ],
    ],
    'passAddOn' => [
        'title'          => 'Pass Add On',
        'title_singular' => 'Pass Add On',
        'fields'         => [
            'id'                      => 'ID',
            'id_helper'               => ' ',
            'title'                   => 'Title',
            'title_helper'            => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'logo'                    => 'Logo',
            'logo_helper'             => ' ',
            'backgroud_image'         => 'Backgroud Image',
            'backgroud_image_helper'  => ' ',
            'start_date'              => 'Contract Start Date',
            'start_date_helper'       => ' ',
            'end_date'                => 'Contract End Date',
            'end_date_helper'         => ' ',
            'pass'                    => 'Pass',
            'pass_helper'             => ' ',
            'created_at'              => 'Created at',
            'created_at_helper'       => ' ',
            'updated_at'              => 'Updated at',
            'updated_at_helper'       => ' ',
            'deleted_at'              => 'Deleted at',
            'deleted_at_helper'       => ' ',
            'pass_add_on_type'        => 'Pass Add On Type',
            'pass_add_on_type_helper' => ' ',
        ],
    ],
    'passAddOnType' => [
        'title'          => 'Pass Add On Type',
        'title_singular' => 'Pass Add On Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'providerType' => [
        'title'          => 'Provider Type',
        'title_singular' => 'Provider Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'customerManagement' => [
        'title'          => 'Customers',
        'title_singular' => 'Customer Management',
    ],
    'customer' => [
        'title'          => 'Customer',
        'title_singular' => 'Customer',
        'fields'         => [
            'id'                        => 'ID',
            'id_helper'                 => ' ',
            'first_name'                => 'First Name',
            'first_name_helper'         => ' ',
            'last_name'                 => 'Last Name',
            'last_name_helper'          => ' ',
            'date_of_birth'             => 'Date Of Birth',
            'date_of_birth_helper'      => ' ',
            'mobile'                    => 'Mobile',
            'mobile_helper'             => ' ',
            'mobile_verified_at'        => 'Mobile Verified At',
            'mobile_verified_at_helper' => ' ',
            'telephone'                 => 'Telephone',
            'telephone_helper'          => ' ',
            'profile_image'             => 'Profile Image',
            'profile_image_helper'      => ' ',
            'password'                  => 'Password',
            'password_helper'           => ' ',
            'pin'                       => 'Pin',
            'pin_helper'                => ' ',
            'created_at'                => 'Created at',
            'created_at_helper'         => ' ',
            'updated_at'                => 'Updated at',
            'updated_at_helper'         => ' ',
            'deleted_at'                => 'Deleted at',
            'deleted_at_helper'         => ' ',
            'gender'                    => 'Gender',
            'gender_helper'             => ' ',
            'otp'                       => 'Otp',
            'otp_helper'                => ' ',
            'otp_expired_at'            => 'Otp Expired At',
            'otp_expired_at_helper'     => ' ',
            'customer_group'            => 'Customer Group',
            'customer_group_helper'     => ' ',
        ],
    ],
    'customerGroup' => [
        'title'          => 'Customer Group',
        'title_singular' => 'Customer Group',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'gender' => [
        'title'          => 'Gender',
        'title_singular' => 'Gender',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'customerAddress' => [
        'title'          => 'Customer Address',
        'title_singular' => 'Customer Address',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Address 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Address 2',
            'address_2_helper'   => ' ',
            'state'              => 'State',
            'state_helper'       => ' ',
            'city'               => 'City',
            'city_helper'        => ' ',
            'postal_code'        => 'Postal Code',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'country'            => 'Country',
            'country_helper'     => ' ',
            'customer'           => 'Customer',
            'customer_helper'    => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'customerEmail' => [
        'title'          => 'Customer Email',
        'title_singular' => 'Customer Email',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'type'                     => 'Type',
            'type_helper'              => ' ',
            'email'                    => 'Email',
            'email_helper'             => ' ',
            'email_verified_at'        => 'Email Verified At',
            'email_verified_at_helper' => ' ',
            'created_at'               => 'Created at',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Updated at',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Deleted at',
            'deleted_at_helper'        => ' ',
            'customer'                 => 'Customer',
            'customer_helper'          => ' ',
        ],
    ],
    'customerDetail' => [
        'title'          => 'Customer Detail',
        'title_singular' => 'Customer Detail',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'customer'          => 'Customer',
            'customer_helper'   => ' ',
            'passport'          => 'Passport',
            'passport_helper'   => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'issuerManagement' => [
        'title'          => 'Issuers',
        'title_singular' => 'Issuer Management',
    ],
    'issuerCatergory' => [
        'title'          => 'Issuer Category',
        'title_singular' => 'Issuer Category',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'issuerLocation' => [
        'title'          => 'Issuer Location',
        'title_singular' => 'Issuer Location',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Address 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Address 2',
            'address_2_helper'   => ' ',
            'state'              => 'State',
            'state_helper'       => ' ',
            'city'               => 'City',
            'city_helper'        => ' ',
            'postal_code'        => 'Postal Code',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'country'            => 'Country',
            'country_helper'     => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
            'issuer'             => 'Issuer',
            'issuer_helper'      => ' ',
            'map_location'              => 'Map Location',
            'map_location_helper'       => ' ',
            'telephone'                 => 'Telephone',
            'telephone_helper'          => ' ',
            'description'               => 'Description',
            'description_helper'        => ' ',
            'phone'                     => 'Phone',
            'phone_helper'              => ' ',
            'email'                     => 'Email',
            'email_helper'              => ' ',
        ],
    ],
    'issuer' => [
        'title'          => 'Issuer',
        'title_singular' => 'Issuer',
        'fields'         => [
            'id'                      => 'ID',
            'id_helper'               => ' ',
            'name'                    => 'Name',
            'name_helper'             => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'phone_code'              => 'Phone Code',
            'phone_code_helper'       => ' ',
            'phone'                   => 'Phone',
            'phone_helper'            => ' ',
            'email'                   => 'Email',
            'email_helper'            => ' ',
            'website_url'             => 'Website Url',
            'website_url_helper'      => ' ',
            'vat_number'              => 'Vat Number',
            'vat_number_helper'       => ' ',
            'brn'                     => 'BRN',
            'brn_helper'              => ' ',
            'status'                  => 'Status',
            'status_helper'           => ' ',
            'logo'                    => 'Logo',
            'logo_helper'             => ' ',
            'issuer_catergory'        => 'Issuer Category',
            'issuer_catergory_helper' => ' ',
            'created_at'              => 'Created at',
            'created_at_helper'       => ' ',
            'slider'                  => 'Slider',
            'slider_helper'           => ' ',
            'updated_at'              => 'Updated at',
            'updated_at_helper'       => ' ',
            'deleted_at'              => 'Deleted at',
            'deleted_at_helper'       => ' ',
            'map_location'              => 'Map Location',
            'map_location_helper'       => ' ',
            'telephone'                 => 'Telephone',
            'telephone_helper'          => ' ',
            'description'               => 'Description',
            'description_helper'        => ' ',
            'phone'                     => 'Phone',
            'phone_helper'              => ' ',
            'email'                     => 'Email',
            'email_helper'              => ' ',
        ],
    ],
    'guideManagement' => [
        'title'          => 'Guides',
        'title_singular' => 'Guide Management',
    ],
    'guideCategory' => [
        'title'          => 'Guide Category',
        'title_singular' => 'Guide Category',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'guide' => [
        'title'          => 'Guide',
        'title_singular' => 'Guide',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'is_question_guide'        => 'Is Question Guide',
            'is_question_guide_helper' => ' ',
            'title'                    => 'Title',
            'title_helper'             => ' ',
            'content'                  => 'Content',
            'content_helper'           => ' ',
            'logo'                     => 'Logo',
            'logo_helper'              => ' ',
            'background_image'         => 'Background Image',
            'background_image_helper'  => ' ',
            'url'                      => 'Url',
            'url_helper'               => ' ',
            'created_at'               => 'Created at',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Updated at',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Deleted at',
            'deleted_at_helper'        => ' ',
            'position'                 => 'Position',
            'position_helper'          => ' ',
        ],
    ],
    'guideQuestion' => [
        'title'          => 'Guide Question',
        'title_singular' => 'Guide Question',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'guide'             => 'Guide',
            'guide_helper'      => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
            'title'             => 'Title',
            'title_helper'      => ' ',
            'image_1'           => 'Image 1',
            'image_1_helper'    => ' ',
            'image_2'           => 'Image 2',
            'image_2_helper'    => ' ',
            'position'          => 'Position',
            'position_helper'   => ' ',
        ],
    ],
    'guideQuestionAnswer' => [
        'title'          => 'Guide Question Answer',
        'title_singular' => 'Guide Question Answer',
        'fields'         => [
            'id'                    => 'ID',
            'id_helper'             => ' ',
            'guide_question'        => 'Guide Question',
            'guide_question_helper' => ' ',
            'customer'              => 'Customer',
            'customer_helper'       => ' ',
            'answer'                => 'Answer',
            'answer_helper'         => ' ',
            'answered_at'           => 'Answered At',
            'answered_at_helper'    => ' ',
            'created_at'            => 'Created at',
            'created_at_helper'     => ' ',
            'updated_at'            => 'Updated at',
            'updated_at_helper'     => ' ',
            'deleted_at'            => 'Deleted at',
            'deleted_at_helper'     => ' ',
        ],
    ],
    'guideCustomerProgress' => [
        'title'          => 'Guide Customer Progress',
        'title_singular' => 'Guide Customer Progress',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'customer'           => 'Customer',
            'customer_helper'    => ' ',
            'answer'             => 'Answer',
            'answer_helper'      => ' ',
            'answered_at'        => 'Answered At',
            'answered_at_helper' => ' ',
            'guide'              => 'Guide',
            'guide_helper'       => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'paymentMethod' => [
        'title'          => 'Payment Method',
        'title_singular' => 'Payment Method',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'vatPercentage' => [
        'title'          => 'Vat Percentage',
        'title_singular' => 'Vat Percentage',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Name',
            'name_helper'       => ' ',
            'value'             => 'Value',
            'value_helper'      => ' ',
            'status'            => 'Status',
            'status_helper'     => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'orderManagement' => [
        'title'          => 'Orders',
        'title_singular' => 'Order Management',
    ],
    'order' => [
        'title'          => 'Order',
        'title_singular' => 'Order',
        'fields'         => [
            'id'                    => 'ID',
            'id_helper'             => ' ',
            'customer'              => 'Customer',
            'customer_helper'       => ' ',
            'issuer'                => 'Issuer',
            'issuer_helper'         => ' ',
            'package'               => 'Package',
            'package_helper'        => ' ',
            'payment_method'        => 'Payment Method',
            'payment_method_helper' => ' ',
            'vat_percentage'        => 'Vat Percentage',
            'vat_percentage_helper' => ' ',
            'status'                => 'Status',
            'status_helper'         => ' ',
            'order_type'            => 'Order Type',
            'order_type_helper'     => ' ',
            'total_vat'             => 'Total Vat',
            'total_vat_helper'      => ' ',
            'sub_total'             => 'Sub Total',
            'sub_total_helper'      => ' ',
            'total'                 => 'Total',
            'total_helper'          => ' ',
            'created_at'            => 'Created at',
            'created_at_helper'     => ' ',
            'updated_at'            => 'Updated at',
            'updated_at_helper'     => ' ',
            'deleted_at'            => 'Deleted at',
            'deleted_at_helper'     => ' ',
        ],
    ],
    'orderItem' => [
        'title'          => 'Order Item',
        'title_singular' => 'Order Item',
        'fields'         => [
            'id'                   => 'ID',
            'id_helper'            => ' ',
            'order'                => 'Order',
            'order_helper'         => ' ',
            'package_price'        => 'Package Price',
            'package_price_helper' => ' ',
            'package'              => 'Package',
            'package_helper'       => ' ',
            'quantity'             => 'Quantity',
            'quantity_helper'      => ' ',
            'price'                => 'Price',
            'price_helper'         => ' ',
            'vat'                  => 'Vat',
            'vat_helper'           => ' ',
            'total_price'          => 'Total Price',
            'total_price_helper'   => ' ',
            'created_at'           => 'Created at',
            'created_at_helper'    => ' ',
            'updated_at'           => 'Updated at',
            'updated_at_helper'    => ' ',
            'deleted_at'           => 'Deleted at',
            'deleted_at_helper'    => ' ',
        ],
    ],
    'passManagement' => [
        'title'          => 'Pass Management',
        'title_singular' => 'Pass Management',
    ],
    'passType' => [
        'title'          => 'Pass Type',
        'title_singular' => 'Pass Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'passStatus' => [
        'title'          => 'Pass Status',
        'title_singular' => 'Pass Status',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'pass' => [
        'title'          => 'Pass',
        'title_singular' => 'Pass',
        'fields'         => [
            'id'                     => 'ID',
            'id_helper'              => ' ',
            'title'                  => 'Title',
            'title_helper'           => ' ',
            'description'            => 'Description',
            'description_helper'     => ' ',
            'logo'                   => 'Logo',
            'logo_helper'            => ' ',
            'backgroud_image'        => 'Backgroud Image',
            'backgroud_image_helper' => ' ',
            'start_date'             => 'Contract Start Date',
            'start_date_helper'      => ' ',
            'end_date'               => 'Contract End Date',
            'end_date_helper'        => ' ',
            'register_period_start_date'      => 'Registration Start Date',
            'register_period_start_date_helper' => ' ',
            'register_period_end_date'        => 'Registration End Date',
            'register_period_end_date_helper' => ' ',
            'pass_status'           => 'Pass Status',
            'pass_status_helper'    => ' ',
            'pass_type'             => 'Pass Type',
            'pass_type_helper'      => ' ',
            'issuer'                => 'Issuer',
            'issuer_helper'         => ' ',
            'created_at'             => 'Created at',
            'created_at_helper'      => ' ',
            'updated_at'             => 'Updated at',
            'updated_at_helper'      => ' ',
            'deleted_at'             => 'Deleted at',
            'deleted_at_helper'      => ' ',
        ],
    ],
    'customerPass' => [
        'title'          => 'Customer Pass',
        'title_singular' => 'Customer Pass',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'customer'          => 'Customer',
            'customer_helper'   => ' ',
            'pass'              => 'Pass',
            'pass_helper'       => ' ',
            'pass_code'         => 'Pass Code',
            'pass_code_helper'  => ' ',
            'price'             => 'Price',
            'price_helper'      => ' ',
            'start_date'        => 'Contract Start Date',
            'start_date_helper' => ' ',
            'end_date'          => 'Contract End Date',
            'end_date_helper'   => ' ',
            'status'            => 'Status',
            'status_helper'     => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'voucherManagement' => [
        'title'          => 'Vouchers',
        'title_singular' => 'Voucher Management',
    ],
    'voucherStatus' => [
        'title'          => 'Voucher Status',
        'title_singular' => 'Voucher Status',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'voucherType' => [
        'title'          => 'Voucher Type',
        'title_singular' => 'Voucher Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'voucher' => [
        'title'          => 'Voucher',
        'title_singular' => 'Voucher',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'voucher_code'               => 'Voucher Code',
            'voucher_code_helper'        => ' ',
            'start_date'                 => 'Contract Start Date',
            'start_date_helper'          => ' ',
            'end_date'                   => 'Contract End Date',
            'end_date_helper'            => ' ',
            'redeemed_at'                => 'Redeemed At',
            'redeemed_at_helper'         => ' ',
            'is_percentage'              => 'Is Percentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Discount Percentage',
            'discount_percentage_helper' => ' ',
            'price'                      => 'Price',
            'price_helper'               => ' ',
            'voucher_status'             => 'Voucher Status',
            'voucher_status_helper'      => ' ',
            'voucher_type'               => 'Voucher Type',
            'voucher_type_helper'        => ' ',
            'customer_pass'              => 'Customer Pass',
            'customer_pass_helper'       => ' ',
            'customer'                   => 'Customer',
            'customer_helper'            => ' ',
            'package'                    => 'Package',
            'package_helper'             => ' ',
            'age_group'                  => 'Age Group',
            'age_group_helper'           => ' ',
            'created_at'                 => 'Created at',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Updated at',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Deleted at',
            'deleted_at_helper'          => ' ',
        ],
    ],

    'passPricing' => [
        'title'          => 'Pass Pricing',
        'title_singular' => 'Pass Pricing',
        'fields'         => [
            'id'                         => 'ID',
            'id_helper'                  => ' ',
            'pass'                       => 'Pass',
            'pass_helper'                => ' ',
            'season'                     => 'Season',
            'season_helper'              => ' ',
            'currency'                   => 'Currency',
            'currency_helper'            => ' ',
            'age_group'                  => 'Age Group',
            'age_group_helper'           => ' ',
            'price'                      => 'Price',
            'price_helper'               => ' ',
            'is_percentage'              => 'Is Percentage',
            'is_percentage_helper'       => ' ',
            'discount_percentage'        => 'Discount Percentage',
            'discount_percentage_helper' => ' ',
            'created_at'                 => 'Created at',
            'created_at_helper'          => ' ',
            'updated_at'                 => 'Updated at',
            'updated_at_helper'          => ' ',
            'deleted_at'                 => 'Deleted at',
            'deleted_at_helper'          => ' ',
            'customer_group'             => 'Customer Group',
            'customer_group_helper'      => ' ',
        ],
    ],
    'packageType' => [
        'title'          => 'Package Types',
        'title_singular' => 'Package Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'passAddOn' => [
        'title'          => 'Pass Add On',
        'title_singular' => 'Pass Add On',
        'fields'         => [
            'id'                      => 'ID',
            'id_helper'               => ' ',
            'title'                   => 'Title',
            'title_helper'            => ' ',
            'description'             => 'Description',
            'description_helper'      => ' ',
            'logo'                    => 'Logo',
            'logo_helper'             => ' ',
            'backgroud_image'         => 'Backgroud Image',
            'backgroud_image_helper'  => ' ',
            'start_date'              => 'Contract Start Date',
            'start_date_helper'       => ' ',
            'end_date'                => 'Contract End Date',
            'end_date_helper'         => ' ',
            'pass'                    => 'Pass',
            'pass_helper'             => ' ',
            'created_at'              => 'Created at',
            'created_at_helper'       => ' ',
            'updated_at'              => 'Updated at',
            'updated_at_helper'       => ' ',
            'deleted_at'              => 'Deleted at',
            'deleted_at_helper'       => ' ',
            'pass_add_on_type'        => 'Pass Add On Type',
            'pass_add_on_type_helper' => ' ',
        ],
    ],
    'passAddOnType' => [
        'title'          => 'Pass Add On Type',
        'title_singular' => 'Pass Add On Type',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'providerAgeGroup' => [
        'title'          => 'Provider Age Group',
        'title_singular' => 'Provider Age Group',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'min_age'            => 'Min Age',
            'min_age_helper'     => ' ',
            'max_age'            => 'Max Age',
            'max_age_helper'     => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'provider'           => 'Provider',
            'provider_helper'    => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'packageLocation' => [
        'title'          => 'Package Location',
        'title_singular' => 'Package Location',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'type'               => 'Type',
            'type_helper'        => ' ',
            'address_1'          => 'Address 1',
            'address_1_helper'   => ' ',
            'address_2'          => 'Address 2',
            'address_2_helper'   => ' ',
            'state'              => 'State',
            'state_helper'       => ' ',
            'city'               => 'City',
            'city_helper'        => ' ',
            'postal_code'        => 'Postal Code',
            'postal_code_helper' => ' ',
            'latitude'           => 'Latitude',
            'latitude_helper'    => ' ',
            'longitude'          => 'Longitude',
            'longitude_helper'   => ' ',
            'package'           => 'Package',
            'package_helper'    => ' ',
            'country'            => 'Country',
            'country_helper'     => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
            'email'                 => 'Email',
            'email_helper'          => ' ',
            'contact_number'        => 'Contact Number',
            'contact_number_helper' => ' ',
            'description'           => 'Description',
            'description_helper'    => ' ',
            'country_code_id'        => 'Country Code',
            'country_code_id_helper' => ' ',
            'map_location'            => 'Map Location',
            'map_location_helper'     => ' ',
            'telephone'               => 'Telephone',
            'telephone_helper'        => ' ',
            'phone'                   => 'Phone',
            'phone_helper'            => ' ',
        ],
    ],
    'bank' => [
        'title'          => 'Bank',
        'title_singular' => 'Bank',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'name'               => 'Name',
            'name_helper'        => ' ',
            'status'             => 'Status',
            'status_helper'      => ' ',
            'description'        => 'Description',
            'description_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'providerBank' => [
        'title'          => 'Provider Bank',
        'title_singular' => 'Provider Bank',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'provider'            => 'Provider',
            'provider_helper'     => ' ',
            'bank'                => 'Bank',
            'bank_helper'         => ' ',
            'account_number'      => 'Account Number',
            'account_number_helper' => ' ',
            'account_name'        => 'Account Name',
            'account_name_helper' => ' ',
            'branch'              => 'Branch',
            'branch_helper'       => ' ',
            'status'              => 'Status',
            'status_helper'       => ' ',
            'created_at'          => 'Created at',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Updated at',
            'updated_at_helper'   => ' ',
            'deleted_at'          => 'Deleted at',
            'deleted_at_helper'   => ' ',
        ],
    ],

];
