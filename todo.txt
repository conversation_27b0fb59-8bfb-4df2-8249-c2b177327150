//

Package type is hidden in sidebar. - done



28/04/2025

1. provider edit - add new tab billing
    billing fields - name, address_1, address_2, status, brn, vat, contact. - done

2. new crud - bank - name, account_number, acct name, description. - done

3. provider edit add provider logo in edit details.

4. package create met provider_id net lao. - done

5. edit provider - fer id redirect to same page. - done

6. dan tou index, fer id redirect to edit page. - done

7. enba breadcrumb, bizin ena title. eg; "Entry ticket - Pamplemousse".

8. change start_date to contract start date. -done

9. tou listing bizin ena status.

10. tou start_date / end_date on same line. - done

11. pricing vin rate_plan dan package. - done

12. rate_plan associated with issuer.

13. pass edit - hide start_date / end_date. - done

14. pass rate calculate - show price breakdown.

//

1. customer address ena 2 fields customer
2. package location


//

3.
In package location change rating to text instead of number
Note need to create migration to change the type of rating from int to string - done

4. In Issuer make location same as provider location - done

5. In Issuer make social same as provider social - done

6. In Issuer make media library like provider media library - done

In package pricing can we change price to selling price and also add a new field name issuer price



prompt : 
In Issuer 
Can you add 
brn
status

proceed with 
1. create migration and run the migration
2. add translation in the lang/en file
3. add fields into create.blade.php
4. add fields into edit.blade.php
5. add fields into fillable in the Issuer model


Voucher 
Pass

air mauritius 
add package like jardin, casela
when 

Date register period

Validity period

url 

Customer open 
enter phone 
base phone link pass voucher 


List value for 
Provider Category
Provider Type 
Package Type
Gender
Issuer Category
Pass Status
Pass Type


Provider Type  
