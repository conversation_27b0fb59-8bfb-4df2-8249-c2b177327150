<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;

class MediaLibraryController extends Controller
{
    use MediaUploadingTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('media_library_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $mediaQuery = Media::query()->where('model_type', 'App\Models\GlobalMedia');

        $search = $request->get('search');
        if (!empty($search)) {
            $mediaQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('file_name', 'like', "%{$search}%")
                      ->orWhere('mime_type', 'like', "%{$search}%");
            });
        }

        $media = $mediaQuery->orderBy('id', 'desc')->paginate(10);

        $media->appends(['search' => $search]);

        // If this is an AJAX request, return JSON with HTML content
        if ($request->ajax() || $request->get('ajax')) {
            $html = view('admin.media_library.partials.media_items', compact('media'))->render();
            $pagination = view('admin.media_library.partials.media_pagination', compact('media'))->render();

            return response()->json([
                'html' => $html,
                'pagination' => $pagination
            ]);
        }

        return view('admin.media_library.index', compact('media', 'search'));
    }

    public function upload(Request $request)
    {
        abort_if(Gate::denies('media_library_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $request->validate([
            'file' => 'required|image|max:20480', // 20MB max
        ]);

        try {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('media', $fileName, 'public');

            // Create media record directly in the media table
            $media = new Media();
            $media->model_type = 'App\Models\GlobalMedia';
            $media->model_id = 0; // Use 0 for global media
            $media->uuid = Str::uuid();
            $media->collection_name = 'global';
            $media->name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $media->file_name = $fileName;
            $media->mime_type = $file->getMimeType();
            $media->disk = 'public';
            $media->conversions_disk = 'public';
            $media->size = $file->getSize();
            $media->manipulations = json_encode([]);
            $media->custom_properties = json_encode([]);
            $media->generated_conversions = json_encode([]);
            $media->responsive_images = json_encode([]);
            $media->order_column = 1;
            $media->save();

            return response()->json([
                'status' => 'success',
                'message' => 'Image uploaded successfully',
                'media' => $media,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Media $media)
    {
        abort_if(Gate::denies('media_library_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        try {
            // Delete the physical file
            if ($media->disk === 'public' && $media->file_name) {
                $filePath = 'media/' . $media->file_name;
                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }
            }

            // Delete the media record
            $media->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Media deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function modal(Request $request)
    {
        abort_if(Gate::denies('media_library_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $mediaQuery = Media::query()->where('model_type', 'App\Models\GlobalMedia');

        $search = $request->get('search');
        if (!empty($search)) {
            $mediaQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('file_name', 'like', "%{$search}%")
                      ->orWhere('mime_type', 'like', "%{$search}%");
            });
        }

        $media = $mediaQuery->orderBy('id', 'desc')->paginate(8);

        $media->appends(['search' => $search]);

        // If this is an AJAX request, return JSON with HTML content
        if ($request->ajax() || $request->get('ajax')) {
            $html = view('admin.media_library.partials.modal_media_items', compact('media'))->render();
            $pagination = view('admin.media_library.partials.modal_media_pagination', compact('media'))->render();

            return response()->json([
                'html' => $html,
                'pagination' => $pagination
            ]);
        }

        return view('admin.media_library.modal', compact('media', 'search'));
    }
}
