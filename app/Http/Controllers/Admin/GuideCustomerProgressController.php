<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyGuideCustomerProgressRequest;
use App\Http\Requests\StoreGuideCustomerProgressRequest;
use App\Http\Requests\UpdateGuideCustomerProgressRequest;
use App\Models\Customer;
use App\Models\Guide;
use App\Models\GuideCustomerProgress;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class GuideCustomerProgressController extends Controller
{
    use CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('guide_customer_progress_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = GuideCustomerProgress::with(['customer', 'guide'])->select(sprintf('%s.*', (new GuideCustomerProgress)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'guide_customer_progress_show';
                $editGate      = 'guide_customer_progress_edit';
                $deleteGate    = 'guide_customer_progress_delete';
                $crudRoutePart = 'guide-customer-progresss';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                $editBadge = '';
                if (auth()->user()->can('guide_customer_progress_edit')) {
                    $editBadge = ' - <a href="' . route('admin.guide-customer-progresss.edit', $row->id) . '" class="no-highlight-edit badge badge-light-primary" onclick="event.stopPropagation();">Edit</a>';
                }
                return $row->id ? '<span style="min-width: 90px; display: inline-block;">' . $row->id . $editBadge . '</span>' : '';
            });
            $table->addColumn('customer_first_name', function ($row) {
                return $row->customer ? $row->customer->first_name : '';
            });

            $table->editColumn('answer', function ($row) {
                return $row->answer ? $row->answer : '';
            });

            $table->addColumn('guide_is_question_guide', function ($row) {
                return $row->guide ? $row->guide->is_question_guide : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'id', 'customer', 'guide']);

            return $table->make(true);
        }

        return view('admin.guideCustomerProgresss.index');
    }

    public function create()
    {
        abort_if(Gate::denies('guide_customer_progress_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $customers = Customer::pluck('first_name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $guides = Guide::pluck('is_question_guide', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.guideCustomerProgresss.create', compact('customers', 'guides'));
    }

    public function store(StoreGuideCustomerProgressRequest $request)
    {
        $guideCustomerProgress = GuideCustomerProgress::create($request->all());

        return redirect()->route('admin.guide-customer-progresss.index');
    }

    public function edit(GuideCustomerProgress $guideCustomerProgresss)
    {
        // dd($guideCustomerProgresss->toArray());
        abort_if(Gate::denies('guide_customer_progress_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $customers = Customer::pluck('first_name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $guides = Guide::pluck('is_question_guide', 'id')->prepend(trans('global.pleaseSelect'), '');

        $guideCustomerProgresss->load('customer', 'guide');

        return view('admin.guideCustomerProgresss.edit', compact('customers', 'guideCustomerProgresss', 'guides'));
    }

    public function update(UpdateGuideCustomerProgressRequest $request, GuideCustomerProgress $guideCustomerProgress)
    {
        $guideCustomerProgress->update($request->all());

        return redirect()->route('admin.guide-customer-progresss.index');
    }

    public function show(GuideCustomerProgress $guideCustomerProgress)
    {
        abort_if(Gate::denies('guide_customer_progress_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $guideCustomerProgress->load('customer', 'guide');

        return view('admin.guideCustomerProgresss.show', compact('guideCustomerProgress'));
    }

    public function destroy(GuideCustomerProgress $guideCustomerProgress)
    {
        abort_if(Gate::denies('guide_customer_progress_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $guideCustomerProgress->delete();

        return back();
    }

    public function massDestroy(MassDestroyGuideCustomerProgressRequest $request)
    {
        $guideCustomerProgresss = GuideCustomerProgress::find(request('ids'));

        foreach ($guideCustomerProgresss as $guideCustomerProgress) {
            $guideCustomerProgress->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
