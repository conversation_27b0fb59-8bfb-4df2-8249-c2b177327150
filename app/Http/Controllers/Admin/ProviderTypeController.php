<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyProviderTypeRequest;
use App\Http\Requests\StoreProviderTypeRequest;
use App\Http\Requests\UpdateProviderTypeRequest;
use App\Models\ProviderType;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Traits\DrawerFormTrait;

class ProviderTypeController extends Controller
{
    use CsvImportTrait,DrawerFormTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('provider_type_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = ProviderType::query()->select(sprintf('%s.*', (new ProviderType)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'provider_type_show';
                $editGate      = 'provider_type_edit';
                $deleteGate    = 'provider_type_delete';
                $crudRoutePart = 'provider-types';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                $id = $row->id ? $row->id : '';
                $editLink = route('admin.provider-types.edit', $row->id);
                return $id ? $id . ' - <a href="' . $editLink . '" class="no-highlight-edit badge badge-light-primary" onclick="event.stopPropagation();">Edit</a>' : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            });
            $table->editColumn('status', function ($row) {
                if ($row->status && isset(ProviderType::STATUS_SELECT[$row->status])) {
                    return trans(ProviderType::STATUS_SELECT[$row->status]);
                }
                return $row->status ? $row->status : '';
            });
            $table->editColumn('description', function ($row) {
                return $row->description ? $row->description : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'id']);

            return $table->make(true);
        }

        return view('admin.providerTypes.index');
    }

    public function create()
    {
        abort_if(Gate::denies('provider_type_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providerTypes.create');
    }

    public function store(StoreProviderTypeRequest $request)
    {
        if ($request->ajax()) {

            $providerType = ProviderType::create($request->all());

            return response()->json([
                'success' => 'Status updated successfully.',
                'providerType' => $providerType
            ]);
        }

        $providerType = ProviderType::create($request->all());

        return redirect()->route('admin.provider-types.index');
    }

    public function edit(ProviderType $providerType)
    {
        abort_if(Gate::denies('provider_type_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providerTypes.edit', compact('providerType'));
    }

    public function update(UpdateProviderTypeRequest $request, ProviderType $providerType)
    {
        if ($request->ajax()) {

            $providerType->update($request->all());

            return response()->json([
                'success' => 'Status updated successfully.',
                'providerType' => $providerType
            ]);
        }

        $providerType->update($request->all());

        return redirect()->route('admin.provider-types.index');
    }

    public function show(ProviderType $providerType)
    {
        abort_if(Gate::denies('provider_type_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $providerType->load('providerTypeProviders');

        return view('admin.providerTypes.show', compact('providerType'));
    }

    public function destroy(ProviderType $providerType)
    {
        abort_if(Gate::denies('provider_type_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $providerType->delete();

        return back();
    }

    public function massDestroy(MassDestroyProviderTypeRequest $request)
    {
        $providerTypes = ProviderType::find(request('ids'));

        foreach ($providerTypes as $providerType) {
            $providerType->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
