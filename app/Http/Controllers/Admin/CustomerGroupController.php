<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyCustomerGroupRequest;
use App\Http\Requests\StoreCustomerGroupRequest;
use App\Http\Requests\UpdateCustomerGroupRequest;
use App\Models\CustomerGroup;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class CustomerGroupController extends Controller
{
    use CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('customer_group_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = CustomerGroup::query()->select(sprintf('%s.*', (new CustomerGroup)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'customer_group_show';
                $editGate      = 'customer_group_edit';
                $deleteGate    = 'customer_group_delete';
                $crudRoutePart = 'customer-groups';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                $id = $row->id ? $row->id : '';
                $editLink = route('admin.customer-groups.edit', $row->id);
                return $id ? $id . ' - <a href="' . $editLink . '" class="no-highlight-edit badge badge-light-primary" onclick="event.stopPropagation();">Edit</a>' : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            });
            $table->editColumn('status', function ($row) {
                return $row->status && isset(CustomerGroup::STATUS_SELECT[$row->status]) ? trans(CustomerGroup::STATUS_SELECT[$row->status]) : '';
            });
            $table->editColumn('description', function ($row) {
                return $row->description ? $row->description : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'id']);

            return $table->make(true);
        }

        return view('admin.customerGroups.index');
    }

    public function create()
    {
        abort_if(Gate::denies('customer_group_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.customerGroups.create');
    }

    public function store(StoreCustomerGroupRequest $request)
    {
        $customerGroup = CustomerGroup::create($request->all());

        return redirect()->route('admin.customer-groups.index');
    }

    public function edit(CustomerGroup $customerGroup)
    {
        abort_if(Gate::denies('customer_group_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.customerGroups.edit', compact('customerGroup'));
    }

    public function update(UpdateCustomerGroupRequest $request, CustomerGroup $customerGroup)
    {
        $customerGroup->update($request->all());

        return redirect()->route('admin.customer-groups.index');
    }

    public function show(CustomerGroup $customerGroup)
    {
        abort_if(Gate::denies('customer_group_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.customerGroups.show', compact('customerGroup'));
    }

    public function destroy(CustomerGroup $customerGroup)
    {
        abort_if(Gate::denies('customer_group_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $customerGroup->delete();

        return back();
    }

    public function massDestroy(MassDestroyCustomerGroupRequest $request)
    {
        $customerGroups = CustomerGroup::find(request('ids'));

        foreach ($customerGroups as $customerGroup) {
            $customerGroup->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
