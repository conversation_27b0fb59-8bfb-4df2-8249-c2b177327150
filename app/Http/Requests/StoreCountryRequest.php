<?php

namespace App\Http\Requests;

use App\Models\Country;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class StoreCountryRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('country_create');
    }

    public function rules()
    {
        return [
            'name' => [
                'string',
                'required',
            ],
            'short_code' => [
                'string',
                'required',
            ],
            'code' => [
                'string',
                'nullable',
            ],
            'phone_code' => [
                'string',
                'nullable',
            ],
            'flag' => [
                'string',
                'nullable',
            ],
            'status' => [
                'string',
                'nullable',
            ],
            'name_translations' => [
                'array',
                'nullable',
            ],
            'name_translations.*' => [
                'string',
                'nullable',
            ],
            'status_translations' => [
                'array',
                'nullable',
            ],
            'status_translations.*' => [
                'string',
                'nullable',
            ],
        ];
    }
}
