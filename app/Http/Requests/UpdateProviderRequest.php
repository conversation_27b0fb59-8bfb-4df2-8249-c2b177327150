<?php

namespace App\Http\Requests;

use App\Models\Provider;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class UpdateProviderRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('provider_edit');
    }

    public function rules()
    {
        return [
            'name' => [
                'string',
                'nullable',
            ],
            'phone' => [
                'string',
                'nullable',
            ],
            'website_url' => [
                'string',
                'nullable',
            ],
            'vat_number' => [
                'string',
                'nullable',
            ],
            'slider' => [
                'array',
            ],
            'telephone' => [
                'string',
                'nullable',
            ],
            'location' => [
                'string',
                'nullable',
            ],
            'map_location' => [
                'string',
                'nullable',
            ],
            'brn' => [
                'string',
                'nullable',
            ],
            'logo' => [
                'string',
                'nullable',
            ],
        ];
    }
}
