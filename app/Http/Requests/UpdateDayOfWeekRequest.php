<?php

namespace App\Http\Requests;

use App\Models\DayOfWeek;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class UpdateDayOfWeekRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('day_of_week_edit');
    }

    public function rules()
    {
        return [
            'name' => [
                'string',
                'nullable',
            ],
            'status' => [
                'string',
                'nullable',
            ],
            'description' => [
                'string',
                'nullable',
            ],
            'name_translations' => [
                'array',
                'nullable',
            ],
            'name_translations.*' => [
                'string',
                'nullable',
            ],
            'status_translations' => [
                'array',
                'nullable',
            ],
            'status_translations.*' => [
                'string',
                'nullable',
            ],
            'description_translations' => [
                'array',
                'nullable',
            ],
            'description_translations.*' => [
                'string',
                'nullable',
            ],
        ];
    }
}
