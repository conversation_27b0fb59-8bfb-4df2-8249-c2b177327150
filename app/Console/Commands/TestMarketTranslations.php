<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Market;

class TestMarketTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:market-translations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Market model translations functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Market model translations...');
        
        // Test that the model loads correctly
        $market = new Market();
        $this->info('✓ Market model instantiated successfully');
        
        // Check if translatable trait is loaded
        $hasTranslateMethod = method_exists($market, 'translate');
        $this->info('✓ Has translate method: ' . ($hasTranslateMethod ? 'Yes' : 'No'));
        
        // Check translatable fields
        $this->info('✓ Translatable fields: ' . implode(', ', $market->translatable));
        
        // Check TYPE_SELECT constants
        $this->info('✓ TYPE_SELECT constants:');
        foreach (Market::TYPE_SELECT as $key => $value) {
            $this->line("  $key => $value");
        }
        
        // Check STATUS_SELECT constants
        $this->info('✓ STATUS_SELECT constants:');
        foreach (Market::STATUS_SELECT as $key => $value) {
            $this->line("  $key => $value");
        }
        
        // Test translation functionality if method exists
        if ($hasTranslateMethod) {
            $this->info('✓ Translation functionality is available');
        }
        
        $this->info('Test completed successfully!');
        return 0;
    }
}
