<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Mail::raw('This is a test email for password reset functionality.', function ($message) {
                $message->to('<EMAIL>')
                        ->subject('Test Email - Password Reset Setup');
            });
            
            $this->info('✅ Email sent successfully to log!');
            $this->info('Check storage/logs/laravel.log for the email content.');
            return 0;
        } catch (\Exception $e) {
            $this->error('❌ Error sending email: ' . $e->getMessage());
            return 1;
        }
    }
}
