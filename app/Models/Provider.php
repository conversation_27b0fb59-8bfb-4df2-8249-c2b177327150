<?php

namespace App\Models;

use App\Traits\Auditable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Provider extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, Auditable, HasFactory;

    public $table = 'providers';

    protected $appends = [
        'slider',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const TYPE_SELECT = [
        'service'  => 'Service',
        'activity' => 'Activity',
    ];

    public const STATUS_SELECT = [
        'active'   => 'Active',
        'inactive' => 'Inactive',
    ];

    public const IMAGE_TYPES = [
        'logo'       => 'Logo',
        'banner'     => 'Banner',
        'slider'     => 'Slider',
        'gallery'    => 'Gallery',
        'background' => 'Background',
        'other'      => 'Other',
    ];

    protected $fillable = [
        'type',
        'name',
        'description',
        'phone_code_id',
        'phone',
        'email',
        'logo',
        'status',
        'website_url',
        'vat_number',
        'provider_catergory_id',
        'provider_type_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'market_id',
        'facebook',
        'instagram',
        'youtube',
        'linkedin',
        'tiktok',
        'telephone',
        'location',
        'map_location',
        'brn',
        'billing_name',
        'billing_address_1',
        'billing_address_2',
        'billing_status',
        'billing_brn',
        'billing_vat',
        'billing_contact',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')->fit('crop', 500, 500);
        $this->addMediaConversion('preview')->fit('crop', 120, 120);
    }

    public function providerPackages()
    {
        return $this->hasMany(Package::class, 'provider_id', 'id');
    }

    public function providerProviderLocations()
    {
        return $this->hasMany(ProviderLocation::class, 'provider_id', 'id');
    }

    public function phone_code()
    {
        return $this->belongsTo(Country::class, 'phone_code_id');
    }

    // Logo is now stored as a direct database field (URL from global media library)
    // The getLogoAttribute() method is no longer needed since we're using a simple string field

    public function provider_catergory()
    {
        return $this->belongsTo(ProviderCatergory::class, 'provider_catergory_id');
    }

    public function getSliderAttribute()
    {
        $files = $this->getMedia('slider');
        $files->each(function ($item) {
            $item->url       = $item->getUrl();
            $item->thumbnail = $item->getUrl('thumb');
            $item->preview   = $item->getUrl('preview');
        });

        return $files;
    }

    public function providerProviderAgeGroups()
    {
        return $this->hasMany(ProviderAgeGroup::class, 'provider_id', 'id');
    }

    public function market()
    {
        return $this->belongsTo(Market::class, 'market_id');
    }

    public function provider_type()
    {
        return $this->belongsTo(ProviderType::class, 'provider_type_id');
    }

    public function providerBanks()
    {
        return $this->hasMany(ProviderBank::class, 'provider_id', 'id');
    }
}
