<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AgeGroup extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, Auditable, HasFactory, Translatable;

    public $table = 'age_groups';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        'active'   => 'Active',
        'inactive' => 'Inactive',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'description',
        'status',
    ];

    protected $fillable = [
        'name',
        'min_age',
        'max_age',
        'description',
        'status',
        'provider_id',
        'name_translations',
        'description_translations',
        'status_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')->fit('crop', 50, 50);
        $this->addMediaConversion('preview')->fit('crop', 120, 120);
    }

    public function ageGroupPackagePrices()
    {
        return $this->hasMany(PackagePrice::class, 'age_group_id', 'id');
    }

    public function provider()
    {
        return $this->belongsTo(Provider::class, 'provider_id');
    }

    public function ageGroupVouchers()
    {
        return $this->hasMany(Voucher::class, 'age_group_id', 'id');
    }
}
