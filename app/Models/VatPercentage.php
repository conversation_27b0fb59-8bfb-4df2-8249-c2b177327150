<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VatPercentage extends Model
{
    use SoftDeletes, Auditable, HasFactory, Translatable;

    public $table = 'vat_percentages';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        'active'   => 'Active',
        'inactive' => 'Inactive',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'status',
    ];

    protected $fillable = [
        'name',
        'value',
        'status',
        'name_translations',
        'status_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function vatPercentageOrders()
    {
        return $this->hasMany(Order::class, 'vat_percentage_id', 'id');
    }
}
