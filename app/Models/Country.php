<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Country extends Model
{
    use SoftDeletes, Auditable, HasFactory, Translatable;

    public $table = 'countries';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        'active'   => 'active',
        'inactive' => 'inactive',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'status',
    ];

    protected $fillable = [
        'name',
        'short_code',
        'code',
        'phone_code',
        'flag',
        'status',
        'name_translations',
        'status_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function phoneCodeProviders()
    {
        return $this->hasMany(Provider::class, 'phone_code_id', 'id');
    }

    public function countryPackageLocations()
    {
        return $this->hasMany(PackageLocation::class, 'country_id', 'id');
    }

    public function countryProviderLocations()
    {
        return $this->hasMany(ProviderLocation::class, 'country_id', 'id');
    }

    public function countryCustomerAddresses()
    {
        return $this->hasMany(CustomerAddress::class, 'country_id', 'id');
    }

    public function countryIssuerLocations()
    {
        return $this->hasMany(IssuerLocation::class, 'country_id', 'id');
    }

    public function phoneCodeIssuers()
    {
        return $this->hasMany(Issuer::class, 'phone_code_id', 'id');
    }
}
