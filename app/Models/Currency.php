<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Currency extends Model
{
    use SoftDeletes, Auditable, HasFactory, Translatable;

    public $table = 'currencies';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const POSITION_SELECT = [
        'start' => 'global.start',
        'end'   => 'global.end',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'symbol',
        'position',
    ];

    protected $fillable = [
        'name',
        'symbol',
        'position',
        'name_translations',
        'symbol_translations',
        'position_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function currencyPackagePrices()
    {
        return $this->hasMany(PackagePrice::class, 'currency_id', 'id');
    }
}
