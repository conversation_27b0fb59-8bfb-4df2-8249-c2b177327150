<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bank extends Model
{
    use SoftDeletes, Auditable, HasFactory, Translatable;

    public $table = 'banks';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        'active' => 'global.active',
        'inactive' => 'global.inactive',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'status',
        'description',
    ];

    protected $fillable = [
        'name',
        'status',
        'description',
        'name_translations',
        'status_translations',
        'description_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function providerBanks()
    {
        return $this->hasMany(ProviderBank::class, 'bank_id', 'id');
    }
}
