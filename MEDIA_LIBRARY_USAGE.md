# Global Media Library Usage Guide

## Overview
The Global Media Library provides a centralized location for managing images across the application. It replaces individual provider media libraries with a unified system.

## Features
- Upload images to a central location
- Browse and search existing images
- Copy image URLs for use in forms
- Delete unused images
- Modal interface for easy integration with forms

## Accessing the Media Library

### Main Interface
Navigate to **Media Library** in the sidebar to access the main interface where you can:
- View all uploaded images
- Upload new images
- Search for specific images
- Delete images
- Copy image URLs

### Modal Interface (for forms)
Use the modal interface to select images for form fields:

```javascript
// Open media library modal for a specific input field
openMediaLibrary('input_field_id');
```

## Integration with Forms

### Basic Usage
```html
<!-- Input field for image URL -->
<input type="text" id="logo_url" name="logo" class="form-control" placeholder="Image URL" />

<!-- Button to open media library -->
<button type="button" class="btn btn-primary btn-sm" onclick="openMediaLibrary('logo_url')">
    Select from Media Library
</button>
```

### Input Group Style
```html
<div class="input-group">
    <input type="text" id="banner_url" name="banner" class="form-control" placeholder="Banner Image URL" />
    <button type="button" class="btn btn-outline-primary" onclick="openMediaLibrary('banner_url')">
        Browse
    </button>
</div>
```

### With Preview
```html
<div class="mb-3">
    <label class="form-label">Logo</label>
    <div class="input-group">
        <input type="text" id="logo_url" name="logo" class="form-control" placeholder="Logo URL" onchange="previewImage(this.value, 'logo_preview')" />
        <button type="button" class="btn btn-outline-primary" onclick="openMediaLibrary('logo_url')">
            Browse
        </button>
    </div>
    <div class="mt-2">
        <img id="logo_preview" src="" alt="Preview" style="max-width: 200px; max-height: 100px; display: none;" />
    </div>
</div>

<script>
function previewImage(url, previewId) {
    const preview = document.getElementById(previewId);
    if (url) {
        preview.src = url;
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}
</script>
```

## JavaScript Helper Functions

Include the media library JavaScript file:
```html
<script src="{{ asset('assets/js/media-library.js') }}"></script>
```

Available helper functions:
- `openMediaLibrary(targetInputId)` - Opens the media library modal
- `createMediaLibraryButton(targetInputId, buttonText, buttonClass)` - Creates a button HTML
- `createMediaLibraryInput(inputId, inputName, inputClass, placeholder, buttonText)` - Creates input group HTML

## Permissions
The following permissions control access to the media library:
- `media_library_access` - View media library
- `media_library_create` - Upload new images
- `media_library_edit` - Edit image details
- `media_library_show` - View individual images
- `media_library_delete` - Delete images

## File Storage
Images are stored in the `storage/app/public/media/` directory and are accessible via the `/storage/media/` URL path.

## Supported Formats
- JPG/JPEG
- PNG
- GIF
- WebP
- Maximum file size: 20MB

## Migration from Provider Media Libraries
The global media library replaces individual provider media libraries. Existing provider-specific media will continue to work, but new uploads should use the global media library for consistency.

## API Endpoints
- `GET /admin/media-library` - Main interface
- `POST /admin/media-library/upload` - Upload new image
- `DELETE /admin/media-library/{media}` - Delete image
- `GET /admin/media-library/modal` - Modal interface (AJAX)

## Example Implementation in Blade Templates

```blade
@extends('layouts.admin')

@section('content')
<div class="card">
    <div class="card-body">
        <form method="POST" action="{{ route('admin.example.store') }}">
            @csrf
            
            <div class="mb-3">
                <label class="form-label">Company Logo</label>
                <div class="input-group">
                    <input type="text" id="logo" name="logo" class="form-control" 
                           placeholder="Select logo from media library" value="{{ old('logo') }}" />
                    <button type="button" class="btn btn-outline-primary" onclick="openMediaLibrary('logo')">
                        <i class="fa fa-images"></i> Browse
                    </button>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">Save</button>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script src="{{ asset('assets/js/media-library.js') }}"></script>
@endsection
```
