<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('markets', function (Blueprint $table) {
            $table->json('name_translations')->nullable();
            $table->json('type_translations')->nullable();
            $table->json('status_translations')->nullable();
            $table->json('description_translations')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('markets', function (Blueprint $table) {
            $table->dropColumn(['name_translations', 'type_translations', 'status_translations', 'description_translations']);
        });
    }
};
