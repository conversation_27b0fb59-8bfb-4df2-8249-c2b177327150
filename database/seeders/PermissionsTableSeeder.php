<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{
    public function run()
    {
        $permissions = [
            [
                'id'    => 1,
                'title' => 'user_management_access',
            ],
            [
                'id'    => 2,
                'title' => 'permission_create',
            ],
            [
                'id'    => 3,
                'title' => 'permission_edit',
            ],
            [
                'id'    => 4,
                'title' => 'permission_show',
            ],
            [
                'id'    => 5,
                'title' => 'permission_delete',
            ],
            [
                'id'    => 6,
                'title' => 'permission_access',
            ],
            [
                'id'    => 7,
                'title' => 'role_create',
            ],
            [
                'id'    => 8,
                'title' => 'role_edit',
            ],
            [
                'id'    => 9,
                'title' => 'role_show',
            ],
            [
                'id'    => 10,
                'title' => 'role_delete',
            ],
            [
                'id'    => 11,
                'title' => 'role_access',
            ],
            [
                'id'    => 12,
                'title' => 'user_create',
            ],
            [
                'id'    => 13,
                'title' => 'user_edit',
            ],
            [
                'id'    => 14,
                'title' => 'user_show',
            ],
            [
                'id'    => 15,
                'title' => 'user_delete',
            ],
            [
                'id'    => 16,
                'title' => 'user_access',
            ],
            [
                'id'    => 17,
                'title' => 'audit_log_show',
            ],
            [
                'id'    => 18,
                'title' => 'audit_log_access',
            ],
            [
                'id'    => 19,
                'title' => 'team_create',
            ],
            [
                'id'    => 20,
                'title' => 'team_edit',
            ],
            [
                'id'    => 21,
                'title' => 'team_show',
            ],
            [
                'id'    => 22,
                'title' => 'team_delete',
            ],
            [
                'id'    => 23,
                'title' => 'team_access',
            ],
            [
                'id'    => 24,
                'title' => 'provider_management_access',
            ],
            [
                'id'    => 25,
                'title' => 'provider_create',
            ],
            [
                'id'    => 26,
                'title' => 'provider_edit',
            ],
            [
                'id'    => 27,
                'title' => 'provider_show',
            ],
            [
                'id'    => 28,
                'title' => 'provider_delete',
            ],
            [
                'id'    => 29,
                'title' => 'provider_access',
            ],
            [
                'id'    => 30,
                'title' => 'provider_catergory_create',
            ],
            [
                'id'    => 31,
                'title' => 'provider_catergory_edit',
            ],
            [
                'id'    => 32,
                'title' => 'provider_catergory_show',
            ],
            [
                'id'    => 33,
                'title' => 'provider_catergory_delete',
            ],
            [
                'id'    => 34,
                'title' => 'provider_catergory_access',
            ],
            [
                'id'    => 35,
                'title' => 'setting_access',
            ],
            [
                'id'    => 36,
                'title' => 'currency_create',
            ],
            [
                'id'    => 37,
                'title' => 'currency_edit',
            ],
            [
                'id'    => 38,
                'title' => 'currency_show',
            ],
            [
                'id'    => 39,
                'title' => 'currency_delete',
            ],
            [
                'id'    => 40,
                'title' => 'currency_access',
            ],
            [
                'id'    => 41,
                'title' => 'day_of_week_create',
            ],
            [
                'id'    => 42,
                'title' => 'day_of_week_edit',
            ],
            [
                'id'    => 43,
                'title' => 'day_of_week_show',
            ],
            [
                'id'    => 44,
                'title' => 'day_of_week_delete',
            ],
            [
                'id'    => 45,
                'title' => 'day_of_week_access',
            ],
            [
                'id'    => 46,
                'title' => 'season_create',
            ],
            [
                'id'    => 47,
                'title' => 'season_edit',
            ],
            [
                'id'    => 48,
                'title' => 'season_show',
            ],
            [
                'id'    => 49,
                'title' => 'season_delete',
            ],
            [
                'id'    => 50,
                'title' => 'season_access',
            ],
            [
                'id'    => 51,
                'title' => 'age_group_create',
            ],
            [
                'id'    => 52,
                'title' => 'age_group_edit',
            ],
            [
                'id'    => 53,
                'title' => 'age_group_show',
            ],
            [
                'id'    => 54,
                'title' => 'age_group_delete',
            ],
            [
                'id'    => 55,
                'title' => 'age_group_access',
            ],
            [
                'id'    => 56,
                'title' => 'package_management_access',
            ],
            [
                'id'    => 57,
                'title' => 'package_create',
            ],
            [
                'id'    => 58,
                'title' => 'package_edit',
            ],
            [
                'id'    => 59,
                'title' => 'package_show',
            ],
            [
                'id'    => 60,
                'title' => 'package_delete',
            ],
            [
                'id'    => 61,
                'title' => 'package_access',
            ],
            [
                'id'    => 62,
                'title' => 'package_schedule_create',
            ],
            [
                'id'    => 63,
                'title' => 'package_schedule_edit',
            ],
            [
                'id'    => 64,
                'title' => 'package_schedule_show',
            ],
            [
                'id'    => 65,
                'title' => 'package_schedule_delete',
            ],
            [
                'id'    => 66,
                'title' => 'package_schedule_access',
            ],
            [
                'id'    => 67,
                'title' => 'package_price_create',
            ],
            [
                'id'    => 68,
                'title' => 'package_price_edit',
            ],
            [
                'id'    => 69,
                'title' => 'package_price_show',
            ],
            [
                'id'    => 70,
                'title' => 'package_price_delete',
            ],
            [
                'id'    => 71,
                'title' => 'package_price_access',
            ],
            [
                'id'    => 72,
                'title' => 'country_create',
            ],
            [
                'id'    => 73,
                'title' => 'country_edit',
            ],
            [
                'id'    => 74,
                'title' => 'country_show',
            ],
            [
                'id'    => 75,
                'title' => 'country_delete',
            ],
            [
                'id'    => 76,
                'title' => 'country_access',
            ],
            [
                'id'    => 77,
                'title' => 'package_location_create',
            ],
            [
                'id'    => 78,
                'title' => 'package_location_edit',
            ],
            [
                'id'    => 79,
                'title' => 'package_location_show',
            ],
            [
                'id'    => 80,
                'title' => 'package_location_delete',
            ],
            [
                'id'    => 81,
                'title' => 'package_location_access',
            ],
            [
                'id'    => 82,
                'title' => 'provider_location_create',
            ],
            [
                'id'    => 83,
                'title' => 'provider_location_edit',
            ],
            [
                'id'    => 84,
                'title' => 'provider_location_show',
            ],
            [
                'id'    => 85,
                'title' => 'provider_location_delete',
            ],
            [
                'id'    => 86,
                'title' => 'provider_location_access',
            ],
            [
                'id'    => 87,
                'title' => 'customer_management_access',
            ],
            [
                'id'    => 88,
                'title' => 'customer_create',
            ],
            [
                'id'    => 89,
                'title' => 'customer_edit',
            ],
            [
                'id'    => 90,
                'title' => 'customer_show',
            ],
            [
                'id'    => 91,
                'title' => 'customer_delete',
            ],
            [
                'id'    => 92,
                'title' => 'customer_access',
            ],
            [
                'id'    => 93,
                'title' => 'gender_create',
            ],
            [
                'id'    => 94,
                'title' => 'gender_edit',
            ],
            [
                'id'    => 95,
                'title' => 'gender_show',
            ],
            [
                'id'    => 96,
                'title' => 'gender_delete',
            ],
            [
                'id'    => 97,
                'title' => 'gender_access',
            ],
            [
                'id'    => 98,
                'title' => 'customer_address_create',
            ],
            [
                'id'    => 99,
                'title' => 'customer_address_edit',
            ],
            [
                'id'    => 100,
                'title' => 'customer_address_show',
            ],
            [
                'id'    => 101,
                'title' => 'customer_address_delete',
            ],
            [
                'id'    => 102,
                'title' => 'customer_address_access',
            ],
            [
                'id'    => 103,
                'title' => 'customer_email_create',
            ],
            [
                'id'    => 104,
                'title' => 'customer_email_edit',
            ],
            [
                'id'    => 105,
                'title' => 'customer_email_show',
            ],
            [
                'id'    => 106,
                'title' => 'customer_email_delete',
            ],
            [
                'id'    => 107,
                'title' => 'customer_email_access',
            ],
            [
                'id'    => 108,
                'title' => 'customer_detail_create',
            ],
            [
                'id'    => 109,
                'title' => 'customer_detail_edit',
            ],
            [
                'id'    => 110,
                'title' => 'customer_detail_show',
            ],
            [
                'id'    => 111,
                'title' => 'customer_detail_delete',
            ],
            [
                'id'    => 112,
                'title' => 'customer_detail_access',
            ],
            [
                'id'    => 113,
                'title' => 'issuer_management_access',
            ],
            [
                'id'    => 114,
                'title' => 'issuer_catergory_create',
            ],
            [
                'id'    => 115,
                'title' => 'issuer_catergory_edit',
            ],
            [
                'id'    => 116,
                'title' => 'issuer_catergory_show',
            ],
            [
                'id'    => 117,
                'title' => 'issuer_catergory_delete',
            ],
            [
                'id'    => 118,
                'title' => 'issuer_catergory_access',
            ],
            [
                'id'    => 119,
                'title' => 'issuer_location_create',
            ],
            [
                'id'    => 120,
                'title' => 'issuer_location_edit',
            ],
            [
                'id'    => 121,
                'title' => 'issuer_location_show',
            ],
            [
                'id'    => 122,
                'title' => 'issuer_location_delete',
            ],
            [
                'id'    => 123,
                'title' => 'issuer_location_access',
            ],
            [
                'id'    => 124,
                'title' => 'issuer_create',
            ],
            [
                'id'    => 125,
                'title' => 'issuer_edit',
            ],
            [
                'id'    => 126,
                'title' => 'issuer_show',
            ],
            [
                'id'    => 127,
                'title' => 'issuer_delete',
            ],
            [
                'id'    => 128,
                'title' => 'issuer_access',
            ],
            [
                'id'    => 129,
                'title' => 'guide_management_access',
            ],
            [
                'id'    => 130,
                'title' => 'guide_category_create',
            ],
            [
                'id'    => 131,
                'title' => 'guide_category_edit',
            ],
            [
                'id'    => 132,
                'title' => 'guide_category_show',
            ],
            [
                'id'    => 133,
                'title' => 'guide_category_delete',
            ],
            [
                'id'    => 134,
                'title' => 'guide_category_access',
            ],
            [
                'id'    => 135,
                'title' => 'guide_create',
            ],
            [
                'id'    => 136,
                'title' => 'guide_edit',
            ],
            [
                'id'    => 137,
                'title' => 'guide_show',
            ],
            [
                'id'    => 138,
                'title' => 'guide_delete',
            ],
            [
                'id'    => 139,
                'title' => 'guide_access',
            ],
            [
                'id'    => 140,
                'title' => 'guide_question_create',
            ],
            [
                'id'    => 141,
                'title' => 'guide_question_edit',
            ],
            [
                'id'    => 142,
                'title' => 'guide_question_show',
            ],
            [
                'id'    => 143,
                'title' => 'guide_question_delete',
            ],
            [
                'id'    => 144,
                'title' => 'guide_question_access',
            ],
            [
                'id'    => 145,
                'title' => 'guide_question_answer_create',
            ],
            [
                'id'    => 146,
                'title' => 'guide_question_answer_edit',
            ],
            [
                'id'    => 147,
                'title' => 'guide_question_answer_show',
            ],
            [
                'id'    => 148,
                'title' => 'guide_question_answer_delete',
            ],
            [
                'id'    => 149,
                'title' => 'guide_question_answer_access',
            ],
            [
                'id'    => 150,
                'title' => 'guide_customer_progress_create',
            ],
            [
                'id'    => 151,
                'title' => 'guide_customer_progress_edit',
            ],
            [
                'id'    => 152,
                'title' => 'guide_customer_progress_show',
            ],
            [
                'id'    => 153,
                'title' => 'guide_customer_progress_delete',
            ],
            [
                'id'    => 154,
                'title' => 'guide_customer_progress_access',
            ],
            [
                'id'    => 155,
                'title' => 'payment_method_create',
            ],
            [
                'id'    => 156,
                'title' => 'payment_method_edit',
            ],
            [
                'id'    => 157,
                'title' => 'payment_method_show',
            ],
            [
                'id'    => 158,
                'title' => 'payment_method_delete',
            ],
            [
                'id'    => 159,
                'title' => 'payment_method_access',
            ],
            [
                'id'    => 160,
                'title' => 'vat_percentage_create',
            ],
            [
                'id'    => 161,
                'title' => 'vat_percentage_edit',
            ],
            [
                'id'    => 162,
                'title' => 'vat_percentage_show',
            ],
            [
                'id'    => 163,
                'title' => 'vat_percentage_delete',
            ],
            [
                'id'    => 164,
                'title' => 'vat_percentage_access',
            ],
            [
                'id'    => 165,
                'title' => 'order_management_access',
            ],
            [
                'id'    => 166,
                'title' => 'order_create',
            ],
            [
                'id'    => 167,
                'title' => 'order_edit',
            ],
            [
                'id'    => 168,
                'title' => 'order_show',
            ],
            [
                'id'    => 169,
                'title' => 'order_delete',
            ],
            [
                'id'    => 170,
                'title' => 'order_access',
            ],
            [
                'id'    => 171,
                'title' => 'order_item_create',
            ],
            [
                'id'    => 172,
                'title' => 'order_item_edit',
            ],
            [
                'id'    => 173,
                'title' => 'order_item_show',
            ],
            [
                'id'    => 174,
                'title' => 'order_item_delete',
            ],
            [
                'id'    => 175,
                'title' => 'order_item_access',
            ],
            [
                'id'    => 176,
                'title' => 'pass_management_access',
            ],
            [
                'id'    => 177,
                'title' => 'pass_type_create',
            ],
            [
                'id'    => 178,
                'title' => 'pass_type_edit',
            ],
            [
                'id'    => 179,
                'title' => 'pass_type_show',
            ],
            [
                'id'    => 180,
                'title' => 'pass_type_delete',
            ],
            [
                'id'    => 181,
                'title' => 'pass_type_access',
            ],
            [
                'id'    => 182,
                'title' => 'pass_create',
            ],
            [
                'id'    => 183,
                'title' => 'pass_edit',
            ],
            [
                'id'    => 184,
                'title' => 'pass_show',
            ],
            [
                'id'    => 185,
                'title' => 'pass_delete',
            ],
            [
                'id'    => 186,
                'title' => 'pass_access',
            ],
            [
                'id'    => 187,
                'title' => 'customer_pass_create',
            ],
            [
                'id'    => 188,
                'title' => 'customer_pass_edit',
            ],
            [
                'id'    => 189,
                'title' => 'customer_pass_show',
            ],
            [
                'id'    => 190,
                'title' => 'customer_pass_delete',
            ],
            [
                'id'    => 191,
                'title' => 'customer_pass_access',
            ],
            [
                'id'    => 192,
                'title' => 'voucher_management_access',
            ],
            [
                'id'    => 193,
                'title' => 'voucher_status_create',
            ],
            [
                'id'    => 194,
                'title' => 'voucher_status_edit',
            ],
            [
                'id'    => 195,
                'title' => 'voucher_status_show',
            ],
            [
                'id'    => 196,
                'title' => 'voucher_status_delete',
            ],
            [
                'id'    => 197,
                'title' => 'voucher_status_access',
            ],
            [
                'id'    => 198,
                'title' => 'voucher_type_create',
            ],
            [
                'id'    => 199,
                'title' => 'voucher_type_edit',
            ],
            [
                'id'    => 200,
                'title' => 'voucher_type_show',
            ],
            [
                'id'    => 201,
                'title' => 'voucher_type_delete',
            ],
            [
                'id'    => 202,
                'title' => 'voucher_type_access',
            ],
            [
                'id'    => 203,
                'title' => 'voucher_create',
            ],
            [
                'id'    => 204,
                'title' => 'voucher_edit',
            ],
            [
                'id'    => 205,
                'title' => 'voucher_show',
            ],
            [
                'id'    => 206,
                'title' => 'voucher_delete',
            ],
            [
                'id'    => 207,
                'title' => 'voucher_access',
            ],
            [
                'id'    => 208,
                'title' => 'market_create',
            ],
            [
                'id'    => 209,
                'title' => 'market_edit',
            ],
            [
                'id'    => 210,
                'title' => 'market_show',
            ],
            [
                'id'    => 211,
                'title' => 'market_delete',
            ],
            [
                'id'    => 212,
                'title' => 'market_access',
            ],
            [
                'id'    => 213,
                'title' => 'customer_group_create',
            ],
            [
                'id'    => 214,
                'title' => 'customer_group_edit',
            ],
            [
                'id'    => 215,
                'title' => 'customer_group_show',
            ],
            [
                'id'    => 216,
                'title' => 'customer_group_delete',
            ],
            [
                'id'    => 217,
                'title' => 'customer_group_access',
            ],
            [
                'id'    => 218,
                'title' => 'pass_pricing_create',
            ],
            [
                'id'    => 219,
                'title' => 'pass_pricing_edit',
            ],
            [
                'id'    => 220,
                'title' => 'pass_pricing_show',
            ],
            [
                'id'    => 221,
                'title' => 'pass_pricing_delete',
            ],
            [
                'id'    => 222,
                'title' => 'pass_pricing_access',
            ],
            [
                'id'    => 223,
                'title' => 'package_type_create',
            ],
            [
                'id'    => 224,
                'title' => 'package_type_edit',
            ],
            [
                'id'    => 225,
                'title' => 'package_type_show',
            ],
            [
                'id'    => 226,
                'title' => 'package_type_delete',
            ],
            [
                'id'    => 227,
                'title' => 'package_type_access',
            ],
            [
                'id'    => 228,
                'title' => 'pass_add_on_create',
            ],
            [
                'id'    => 229,
                'title' => 'pass_add_on_edit',
            ],
            [
                'id'    => 230,
                'title' => 'pass_add_on_show',
            ],
            [
                'id'    => 231,
                'title' => 'pass_add_on_delete',
            ],
            [
                'id'    => 232,
                'title' => 'pass_add_on_access',
            ],
            [
                'id'    => 233,
                'title' => 'pass_add_on_type_create',
            ],
            [
                'id'    => 234,
                'title' => 'pass_add_on_type_edit',
            ],
            [
                'id'    => 235,
                'title' => 'pass_add_on_type_show',
            ],
            [
                'id'    => 236,
                'title' => 'pass_add_on_type_delete',
            ],
            [
                'id'    => 237,
                'title' => 'pass_add_on_type_access',
            ],
            [
                'id'    => 238,
                'title' => 'provider_type_create',
            ],
            [
                'id'    => 239,
                'title' => 'provider_type_edit',
            ],
            [
                'id'    => 240,
                'title' => 'provider_type_show',
            ],
            [
                'id'    => 241,
                'title' => 'provider_type_delete',
            ],
            [
                'id'    => 242,
                'title' => 'provider_type_access',
            ],
            [
                'id'    => 243,
                'title' => 'provider_season_create',
            ],
            [
                'id'    => 244,
                'title' => 'provider_season_edit',
            ],
            [
                'id'    => 245,
                'title' => 'provider_season_show',
            ],
            [
                'id'    => 246,
                'title' => 'provider_season_delete',
            ],
            [
                'id'    => 247,
                'title' => 'provider_season_access',
            ],
            [
                'id'    => 248,
                'title' => 'provider_age_group_create',
            ],
            [
                'id'    => 249,
                'title' => 'provider_age_group_edit',
            ],
            [
                'id'    => 250,
                'title' => 'provider_age_group_show',
            ],
            [
                'id'    => 251,
                'title' => 'provider_age_group_delete',
            ],
            [
                'id'    => 252,
                'title' => 'provider_age_group_access',
            ],
            [
                'id'    => 253,
                'title' => 'profile_password_edit',
            ],
            [
                'id'    => 254,
                'title' => 'bank_create',
            ],
            [
                'id'    => 255,
                'title' => 'bank_edit',
            ],
            [
                'id'    => 256,
                'title' => 'bank_show',
            ],
            [
                'id'    => 257,
                'title' => 'bank_delete',
            ],
            [
                'id'    => 258,
                'title' => 'bank_access',
            ],
            [
                'id'    => 259,
                'title' => 'provider_bank_create',
            ],
            [
                'id'    => 260,
                'title' => 'provider_bank_edit',
            ],
            [
                'id'    => 261,
                'title' => 'provider_bank_show',
            ],
            [
                'id'    => 262,
                'title' => 'provider_bank_delete',
            ],
            [
                'id'    => 263,
                'title' => 'provider_bank_access',
            ],
            [
                'id'    => 264,
                'title' => 'pass_status_create',
            ],
            [
                'id'    => 265,
                'title' => 'pass_status_edit',
            ],
            [
                'id'    => 266,
                'title' => 'pass_status_show',
            ],
            [
                'id'    => 267,
                'title' => 'pass_status_delete',
            ],
            [
                'id'    => 268,
                'title' => 'pass_status_access',
            ],
            [
                'id'    => 269,
                'title' => 'media_library_create',
            ],
            [
                'id'    => 270,
                'title' => 'media_library_edit',
            ],
            [
                'id'    => 271,
                'title' => 'media_library_show',
            ],
            [
                'id'    => 272,
                'title' => 'media_library_delete',
            ],
            [
                'id'    => 273,
                'title' => 'media_library_access',
            ],
        ];

        Permission::insert($permissions);
    }
}
