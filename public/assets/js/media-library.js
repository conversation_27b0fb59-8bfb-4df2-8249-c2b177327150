/**
 * Global Media Library Helper
 * 
 * Usage:
 * 1. Add a button with onclick="openMediaLibrary('input_id')"
 * 2. The selected image URL will be populated in the input field with the given ID
 * 
 * Example:
 * <input type="text" id="logo_url" name="logo" class="form-control" />
 * <button type="button" class="btn btn-primary" onclick="openMediaLibrary('logo_url')">
 *     Select from Media Library
 * </button>
 */

// Global function to open media library
function openMediaLibrary(targetInputId) {
    if (window.MediaLibrary) {
        window.MediaLibrary.open(targetInputId);
    } else {
        console.error('Media Library not loaded');
    }
}

// Helper function to create media library button
function createMediaLibraryButton(targetInputId, buttonText = 'Select from Media Library', buttonClass = 'btn btn-primary btn-sm') {
    return `<button type="button" class="${buttonClass}" onclick="openMediaLibrary('${targetInputId}')">${buttonText}</button>`;
}

// Helper function to create input with media library button
function createMediaLibraryInput(inputId, inputName, inputClass = 'form-control', placeholder = 'Image URL', buttonText = 'Browse') {
    return `
        <div class="input-group">
            <input type="text" id="${inputId}" name="${inputName}" class="${inputClass}" placeholder="${placeholder}" />
            <button type="button" class="btn btn-outline-primary" onclick="openMediaLibrary('${inputId}')">${buttonText}</button>
        </div>
    `;
}
